<!--src\views\public\course\Course_Center\Overview\index.vue-->
<template>
  <div class="anchor-container">
    <el-row class="container-row">
      <!-- 左侧导航 -->
      <el-col :span="4" class="anchor-column">
        <div class="sticky-anchor">
          <el-anchor direction="vertical" offset="80" @click="handleAnchorClick">
            <el-anchor-link href="#part1" title="课程核心数据" />
            <el-anchor-link href="#part2" title="课程背景" />
            <el-anchor-link href="#part3" title="课程定位" />
            <el-anchor-link href="#part4" title="课程目标" />
            <el-anchor-link href="#part5" title="课程设计原则" />
            <el-anchor-link href="#part6" title="课程特色" />
            <el-anchor-link href="#part7" title="课程知识逻辑" />
            <el-anchor-link href="#part8" title="教学计划表" />
            <el-anchor-link href="#part9" title="课程公告" />
          </el-anchor>
        </div>
      </el-col>

      <!-- 右侧内容 -->
      <el-col :span="20" class="content-column">
        <!-- 课程核心数据 -->
        <section id="part1" class="content-section">
          <span class="card-title">课程核心数据</span>
          <div class="card-grid">
            <!-- 知识点 -->
            <div class="card card-blue">
              <div class="card-label">知识点</div>
              <div class="card-value">138</div>
            </div>

            <!-- 知识节点和知识模块 -->
            <div class="knowledge-card">
              <div class="small-card  top-card ">
                <div class="card-label">知识节点</div>
                <div class="card-value">937</div>
              </div>
              <div class="small-card  bottom-card card-lightblue">
                <div class="card-label">知识模块</div>
                <div class="card-value">8</div>
              </div>
            </div>

            <!-- 教学资源 -->
            <div class="card card-lightblue">
              <div class="card-label">教学资源</div>
              <div class="card-value">1003</div>
            </div>
            <!-- 外部资源 -->
            <div class="card card-lightgreen">
              <div class="card-label">引用外部资源</div>
              <div class="card-value">523</div>
            </div>

            <!-- 能力目标 -->
            <div class="knowledge-card">
              <div class="small-card  top-card ">
                <div class="card-label">能力目标</div>
                <div class="card-value">14</div>
              </div>
              <div class="small-card bottom-card card-lightblue">
                <div class="card-label">问题图谱</div>
                <div class="card-value">101</div>
              </div>
            </div>

            <!-- AI出题 -->
            <div class="card  ai-card">
              <div class="card-label">AI自动出题</div>
              <div class="card-value">130</div>
              <div class="card-badge">AI智能</div>
            </div>
          </div>
        </section>
        <!-- 课程背景 -->
        <section id="part2" class="content-section">
          <span class="card-title">课程背景</span>
          <div class="Description">
            《机械制造基础》是机械类专业的专业基础课，是机械工程创新人才培养的基石，是基础课与专业课的纽带。为解决目前 《机械制造基础》课程教学中存在的矛盾问题，
            即：概念繁多结构零散导致学生系统思维薄弱、教学资源单一缺乏工程案例导致创新潜质不足、重知识传授轻工程应用导致学生工程能力不足。
            课程团队针对课程痛点问题，以工程反哺、实例贯穿、虚实结合为基石，通过重构教学内容、丰富教学资源、创新教学方法、改革保障评价和思政融入引领等方面进行创新
            实施，全方位培养学生的高阶思维、工程意识、创新能力和家国情怀，实现了学生的知识传授、能力培养和价值塑造。
          </div>

        </section>
        <!-- 课程定位 -->
        <section id="part3" class="content-section">
          <span class="card-title">课程定位</span>
          <div style="margin-top: 1.04vw;">
            <span class="course-position-meta">专业课程基础</span>
            <span class="course-position-meta">适用专业：机械制造（含留学生）、机械电子工程、工业设计、工业工程</span>
            <span class="course-position-meta">对课程要求：本课程要求学生具备机械控制工程基础、复变函数与积分变换、机械设计等相关知识</span>
            <span class="course-position-meta">对后续课程的支撑：本课程课程要求学生具备机械控制工程基础、复变函数与积分变换、机械设计等</span>
          </div>
          <div class="Description">
            《机械制造基础》是机械类专业的专业基础课，是机械工程创新人才培养的基石，是基础课与专业课的纽带。为解决目前 《机械制造基础》课程教学中存在的矛盾问题，
            即：概念繁多结构零散导致学生系统思维薄弱、教学资源单一缺乏工程案例导致创新潜质不足、重知识传授轻工程应用导致学生工程能力不足。
            课程团队针对课程痛点问题，以工程反哺、实例贯穿、虚实结合为基石，通过重构教学内容、丰富教学资源、创新教学方法、改革保障评价和思政融入引领等方面进行创新
            实施，全方位培养学生的高阶思维、工程意识、创新能力和家国情怀，实现了学生的知识传授、能力培养和价值塑造。
          </div>
        </section>
        <!-- 课程目标 -->
        <section id="part4" class="content-section">
          <span class="card-title">课程目标</span>
          <div class="Description">
            1.知识目标
            <br>
            《阶思维、工程意识、创新能力和家国情怀，实现了学生的知识传授、能力培养和价值塑造。
            <br>
            2.能力目标
            <br>
            《阶思维、工程意识、创新能力和家国情怀，实现了学生的知识传授、能力培养和价值塑造。
            <br>
            3.素质目标
            <br>
            《阶思维、工程意识、创新能力和家国情怀，实现了学生的知识传授、能力培养和价值塑造。
          </div>
        </section>
        <!-- 课程设计原则 -->
        <section id="part5" class="content-section">
          <span class="card-title">课程设计原则</span>
          <div class="Description">
            《机械制造基础》是机械类专业的专业基础课，是机械工程创新人才培养的基石，是基础课与专业课的纽带。为解决目前 《机械制造基础》课程教学中存在的矛盾问题，
            即：概念繁多结构零散导致学生系统思维薄弱、教学资源单一缺乏工程案例导致创新潜质不足、重知识传授轻工程应用导致学生工程能力不足。
            课程团队针对课程痛点问题，以工程反哺、实例贯穿、虚实结合为基石，通过重构教学内容、丰富教学资源、创新教学方法、改革保障评价和思政融入引领等方面进行创新
            实施，全方位培养学生的高阶思维、工程意识、创新能力和家国情怀，实现了学生的知识传授、能力培养和价值塑造。
          </div>
        </section>
        <!-- 课程特色 -->
        <section id="part6" class="content-section">
          <span class="card-title">课程特色</span>
          <div class="Description">
            《机械制造基础》是机械类专业的专业基础课，是机械工程创新人才培养的基石，是基础课与专业课的纽带。为解决目前 《机械制造基础》课程教学中存在的矛盾问题，
            即：概念繁多结构零散导致学生系统思维薄弱、教学资源单一缺乏工程案例导致创新潜质不足、重知识传授轻工程应用导致学生工程能力不足。
            课程团队针对课程痛点问题，以工程反哺、实例贯穿、虚实结合为基石，通过重构教学内容、丰富教学资源、创新教学方法、改革保障评价和思政融入引领等方面进行创新
            实施，全方位培养学生的高阶思维、工程意识、创新能力和家国情怀，实现了学生的知识传授、能力培养和价值塑造。
          </div>
        </section>
        <!-- 课程知识逻辑 -->
        <section id="part7" class="content-section">
          <span class="card-title">课程知识逻辑</span>
          <div class="Description">
            《机械制造基础》是机械类专业的专业基础课，是机械工程创新人才培养的基石，是基础课与专业课的纽带。为解决目前 《机械制造基础》课程教学中存在的矛盾问题，
            即：概念繁多结构零散导致学生系统思维薄弱、教学资源单一缺乏工程案例导致创新潜质不足、重知识传授轻工程应用导致学生工程能力不足。
            课程团队针对课程痛点问题，以工程反哺、实例贯穿、虚实结合为基石，通过重构教学内容、丰富教学资源、创新教学方法、改革保障评价和思政融入引领等方面进行创新
            实施，全方位培养学生的高阶思维、工程意识、创新能力和家国情怀，实现了学生的知识传授、能力培养和价值塑造。
          </div>
        </section>
        <!-- 教学计划表 -->
        <section id="part8" class="content-section">
          <div class="teaching-plan-header">
            <span class="card-title">课程计划表</span>
            <div class="meta-container">
              <section class="CourseMeta">
                <span class="meta-label">学分</span>
                <span class="meta-value">2.0</span>
              </section>
              <section class="CourseMeta">
                <span class="meta-label">学时</span>
                <span class="meta-value">2.0</span>
              </section>
            </div>
          </div>
          <div class="teaching-plan-container">
            <div class="teaching-plan-row">
              <div class="teaching-plan-cell left-cell">生命体征监测(心电图操作及判读)</div>
              <div class="teaching-plan-cell right-cell">4.0学时</div>
            </div>
            <div class="teaching-plan-row">
              <div class="teaching-plan-cell left-cell">徒手心肺复苏术(单、双人)</div>
              <div class="teaching-plan-cell right-cell">4.0学时</div>
            </div>
            <div class="teaching-plan-row">
              <div class="teaching-plan-cell left-cell">手卫生、无菌隔离术、常用器械识别与使用</div>
              <div class="teaching-plan-cell right-cell">4.0学时</div>
            </div>
            <div class="teaching-plan-row">
              <div class="teaching-plan-cell left-cell">注射技术</div>
              <div class="teaching-plan-cell right-cell">4.0学时</div>
            </div>
            <div class="teaching-plan-row">
              <div class="teaching-plan-cell left-cell">吸氧术、简易呼吸器的使用</div>
              <div class="teaching-plan-cell right-cell">4.0学时</div>
            </div>
            <div class="teaching-plan-row">
              <div class="teaching-plan-cell left-cell">动脉采血术、动脉穿刺术</div>
              <div class="teaching-plan-cell right-cell">4.0学时</div>
            </div>
            <div class="teaching-plan-row">
              <div class="teaching-plan-cell left-cell">换药、拆线术、导尿术</div>
              <div class="teaching-plan-cell right-cell">4.0学时</div>
            </div>
            <div class="teaching-plan-row">
              <div class="teaching-plan-cell left-cell">吸痰术、胃管置入术</div>
              <div class="teaching-plan-cell right-cell">4.0学时</div>
            </div>
          </div>
        </section>
        <!-- 课程公告 -->
        <section id="part9" class="content-section">
          <span class="card-title">课程公告</span>
          <span style="color: gray; font-size: 1.04vw; ">(仅显示开课教师发布的公告)</span>
          <div class="Description">
            《机械制造基础》是机械类专业的专业基础课，是机械工程创新人才培养的基石，是基础课与专业课的纽带。为解决目前 《机械制造基础》课程教学中存在的矛盾问题，
            即：概念繁多结构零散导致学生系统思维薄弱、教学资源单一缺乏工程案例导致创新潜质不足、重知识传授轻工程应用导致学生工程能力不足。
            课程团队针对课程痛点问题，以工程反哺、实例贯穿、虚实结合为基石，通过重构教学内容、丰富教学资源、创新教学方法、改革保障评价和思政融入引领等方面进行创新
            实施，全方位培养学生的高阶思维、工程意识、创新能力和家国情怀，实现了学生的知识传授、能力培养和价值塑造。
          </div>
        </section>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { getCourseDetail } from '@/api/public/course/course';

const route = useRoute();
const courseDetail = ref(null);

const handleAnchorClick = (e) => {
  e.preventDefault();
  const href = e.target.getAttribute('href');
  if (href) {
    const target = document.querySelector(href);
    target?.scrollIntoView({ behavior: 'smooth' });
  }
};

onMounted(async () => {
  try {
    console.log('5. 开始获取课程详情数据，课程ID:', route.params.courseId); // 调试点5
    const res = await getCourseDetail(route.params.courseId);
    console.log('5.1 获取到的课程详情数据:', res); // 调试点5.1
    courseDetail.value = res.result;
  } catch (error) {
    console.error('5.2 获取课程详情失败:', error); // 调试点5.2
  }
});
</script>

<style lang="scss" scoped>
// 公共变量
$primary-padding: 0.925vw;
$border-radius: 0.5vw;
$box-shadow: 0 0.093vw 0.37vw rgba(0, 0, 0, 0.1);
$content-padding-top: 4vw;
$card-gap: 2vw;
$meta-size: 1vw;

/* 颜色样式 */
$color-blue: #3b82f6;
$color-lightblue: #dbeafe;
$color-lightgreen: #dcfce7;
$color-gray: #f3f4f6;

.anchor-container {
  width: 100%;
  padding: $primary-padding;
  min-height: 100vh;
}

.anchor-column {
  padding-right: $primary-padding;

  .sticky-anchor {
    position: sticky;
    top: 1vw;
    height: fit-content;
    background: white;
    padding: 0.05208vw;
    border-radius: $border-radius;
    box-shadow: $box-shadow;

    ::v-deep .el-anchor__list {
      padding-left: 1vw;  // 调整左侧内边距
      margin: 0.5vw 0;     // 调整上下外边距
    }
    ::v-deep .el-anchor__link {
      color: $text-color;
      font-size: 0.95vw;
      max-height:3vw;
      padding: 0.5vw;
      line-height: 2.5;
    }



    ::v-deep .el-anchor__link.is-active {
      color: $primary-color !important;
    }

    ::v-deep .el-anchor__marker {
      background-color: $primary-color !important;
      width: 0.152vw;
    }

  }
}

.content-column {
  padding-left: $primary-padding;
}

.content-section {
  padding-top: $content-padding-top;
  /* min-height: 2.604vw;
 */
  .card-title {
    font-size: 2.35vw;
  }

  .Description {
    color: #333;
    font-family: "Alibaba PuHuiTi 3.0";
    font-size: 1vw;
    line-height: 1.66667vw;
    margin-top: 1vw;
  }
}

/* 课程核心数据 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, 10vw);
  height: 14.875vw;
  gap: $card-gap;
  margin-top: 1vw;

  .card,
  .small-card {
    width: 11vw;
    padding: 1vw;
    border-radius: $border-radius;
    box-shadow: 0 0.104vw 0.312vw rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    justify-content: space-between;


    .card-label {
      align-self: flex-start;
      font-size: 1.35vw;
      margin-bottom: 1vw;
    }

    .card-value {
      font-size: 1.25vw;
      font-weight: bold;
      align-self: flex-start;
    }
  }

  .small-card {
    padding: 0.5625vw;
  }
}

.card-blue {
  background-color: $color-blue;
  color: white;
}

.card-lightblue {
  background-color: $color-lightblue;
}

.card-lightgreen {
  background-color: $color-lightgreen;
}

.card-gray {
  background-color: $color-gray;
}

/* 知识节点模块 */
.knowledge-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;

  .top-card,
  .bottom-card {
    flex: 1;

    &:not(:last-child) {
      margin-bottom: 0.26vw;
    }

  }
}

/* AI徽标 */
.ai-card {
  position: relative;

  .card-badge {
    position: absolute;
    top: 0.417vw;
    right:0.417vw;
    font-size: 0.572vw;
    background-color: #e0f2fe;
    color: $primary-color;
    padding: 0.104vw 0.312vw;
    border-radius: 0.312vw;
  }
}

/* 课程定位元数据 */
.course-position-meta {
  display: inline-block;
  padding:0 0.52vw;
  border-radius: $border-radius;
  background: #f7f7f7;
  color: #000;
  font-family: "Alibaba PuHuiTi 3.0";
  font-size: $meta-size;
  font-weight: 700;
  opacity: 0.9;
  margin: 0 0.52vw  0;
}

/* 教学计划表 */
.teaching-plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 1vw;
  margin-bottom: 1.04vw;

  .meta-container {
    display: flex;
    gap: 0.52vw;

    .CourseMeta {
      display: flex;
      min-width: 5.2vw;
      justify-content: space-between;
      padding: 0.65vw;
      border-radius: $border-radius;
      background: #fafafa;

      .meta-label,
      .meta-value {
        font-family: "Alibaba PuHuiTi 3.0";
        font-size: 0.9375vw;
        font-weight: 700;
        opacity: 0.9;
      }

      .meta-label {
        margin-right: 0.417vw;
      }
    }
  }
}

.teaching-plan-container {
  max-height: 21.2625vw;
  overflow-y: auto;
  border: 0.052vw solid #ebebeb;
  padding: 1.04vw;
  border-radius: 1.25vw;

  .teaching-plan-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.625vw;
    border-radius: $border-radius;
    background: #fafafa;
    box-shadow: 0 0.104vw 0.208vw rgba(0, 0, 0, 0.1);
    font-size: 1vw;
    margin-bottom: 0.725vw;

    .teaching-plan-cell {
      flex: 1;
      margin: 0 0.52vw;
      white-space: nowrap; // 防止内容换行

      &.left-cell {
        text-align: left;
      }

      &.right-cell {
        text-align: right;
      }
    }
  }
}
</style>