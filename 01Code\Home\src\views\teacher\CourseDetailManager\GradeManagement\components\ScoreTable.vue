<template>
  <div class="score-container">
    <!-- 顶部控制栏 -->
    <div class="control-bar">
      <div class="class-selector">
        <label for="class-select">班级：</label>
        <select id="class-select" v-model="selectedClass">
          <option v-for="classItem in classes" :key="classItem.id" :value="classItem.id">
            {{ classItem.name }}
          </option>
        </select>
      </div>
      
      <div class="right-controls">
        <button class="download-btn" @click="exportScores">
          <span class="icon">↓</span> 下载平时成绩
        </button>
        
        <div class="search-box">
          <input
            type="text"
            placeholder="请输入学生姓名或学号"
            v-model="searchQuery"
            @input="filterStudents"
          />
          <span class="search-icon">🔍</span>
        </div>
      </div>
    </div>

    <!-- 数据截止时间 -->
    <div class="data-cutoff">
      数据截止到 {{ cutoffDate }}
    </div>

    <!-- 成绩表格 -->
    <div class="table-container">
      <table class="score-table">
        <thead>
          <tr>
            <th @click="sortBy('id')">
              学号
              <span class="sort-icon" :class="{ 'active': sortField === 'id' }">
                {{ sortOrder === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th>姓名</th>
            <th @click="sortBy('progress')">
              学习进度分
              <span class="sort-icon" :class="{ 'active': sortField === 'progress' }">
                {{ sortOrder === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th @click="sortBy('interaction')">
              互动次数
              <span class="sort-icon" :class="{ 'active': sortField === 'interaction' }">
                {{ sortOrder === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th @click="sortBy('total')">
              平时成绩总分
              <span class="sort-icon" :class="{ 'active': sortField === 'total' }">
                {{ sortOrder === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="student in filteredStudents" :key="student.id">
            <td>{{ student.id }}</td>
            <td>{{ student.name }}</td>
            <td>{{ student.progress }}%</td>
            <td>{{ student.interaction }}次</td>
            <td class="total-score">{{ student.total }}分</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 模拟数据
const classes = ref([
  { id: 1, name: '数字媒体技术111班(1)' },
  { id: 2, name: '数字媒体技术111班(2)' },
  { id: 3, name: '数字媒体技术112班(1)' }
])

const students = ref([
  {
    id: '2300470107',
    name: '潘丽珊',
    progress: 0,
    interaction: 0,
    total: 0
  },
  {
    id: '2300470108',
    name: '张三',
    progress: 65,
    interaction: 12,
    total: 77
  },
  {
    id: '2300470109',
    name: '李四',
    progress: 80,
    interaction: 8,
    total: 88
  },
  {
    id: '2300470110',
    name: '王五',
    progress: 92,
    interaction: 15,
    total: 107
  }
])

// 状态管理
const selectedClass = ref(1)
const searchQuery = ref('')
const sortField = ref('id')
const sortOrder = ref('asc')
const cutoffDate = ref('06-29') // 数据截止日期

// 过滤和排序学生数据
const filteredStudents = computed(() => {
  let result = [...students.value]
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(s => 
      s.name.toLowerCase().includes(query) || 
      s.id.toLowerCase().includes(query)
    )
  }
  
  // 排序
  if (sortField.value) {
    result.sort((a, b) => {
      let comparison = 0
      if (a[sortField.value] > b[sortField.value]) {
        comparison = 1
      } else if (a[sortField.value] < b[sortField.value]) {
        comparison = -1
      }
      return sortOrder.value === 'asc' ? comparison : -comparison
    })
  }
  
  return result
})

// 方法
const sortBy = (field) => {
  if (sortField.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortField.value = field
    sortOrder.value = 'asc'
  }
}

const filterStudents = () => {
  // 搜索功能已经在计算属性中实现
}

const exportScores = () => {
  // 导出成绩功能
  console.log('导出平时成绩')
  // 这里可以添加实际的导出逻辑，如调用API或生成Excel
}
</script>

<style lang="scss" scoped>
.score-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Microsoft YaHei', sans-serif;
  padding: 20px;
}

.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
  
  .class-selector {
    select {
      padding: 8px 12px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-size: 14px;
      min-width: 180px;
    }
  }
  
  .right-controls {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .download-btn {
    padding: 8px 15px;
    background-color: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #66b1ff;
    }
    
    .icon {
      font-weight: bold;
    }
  }
  
  .search-box {
    position: relative;
    
    input {
      padding: 8px 12px 8px 32px;
      border-radius: 4px;
      border: 1px solid #ddd;
      width: 250px;
      font-size: 14px;
    }
    
    .search-icon {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

.data-cutoff {
  color: #888;
  font-size: 14px;
  margin-bottom: 10px;
  padding-left: 5px;
}

.table-container {
  width: 100%;
  overflow-x: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.score-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
  
  th, td {
    padding: 12px 15px;
    text-align: center;
    border-bottom: 1px solid #eee;
  }
  
  th {
    background-color: #f8f9fa;
    font-weight: 600;
    white-space: nowrap;
    cursor: pointer;
    
    &:first-child {
      text-align: left;
    }
    
    .sort-icon {
      opacity: 0;
      margin-left: 5px;
      transition: opacity 0.2s;
      
      &.active {
        opacity: 1;
      }
    }
    
    &:hover .sort-icon {
      opacity: 0.5;
    }
  }
  
  td {
    &:first-child {
      text-align: left;
    }
    
    &.total-score {
      font-weight: bold;
      color: #409eff;
    }
  }
  
  tr:hover {
    background-color: #f8f9fa;
  }
}
</style>