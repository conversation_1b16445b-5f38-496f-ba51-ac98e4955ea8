import request from '@/api/service'

//创建签到表
export function createAttendance(data) {
  return request.post('/teacher/attendance/create', data)
}

//获取全部签到表
export function fetchAttendanceList() {
  return request.get('/teacher/attendance/list');
}

//删除签到表
export function deleteAttendance(attendanceId) {
  return request.delete(`/teacher/attendance/delete/${attendanceId}`);
}

//更新签到表
export function updateAttendance(data) {
  return request.put('/teacher/attendance/update', data);
}