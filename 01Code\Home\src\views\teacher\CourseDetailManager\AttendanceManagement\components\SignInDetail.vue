<template>
  <div class="sign-in-detail-container">
    <!-- 顶部标题和返回按钮 -->
    <div class="header-section">
      <button class="back-button" @click="goBack">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
        </svg>
        返回
      </button>
      <h2 class="sign-in-title">{{ signInName }}</h2>
    </div>

    <!-- 筛选控制区域 -->
    <div class="filter-controls">
      <div class="filter-item">
        <label for="class-select">班级：</label>
        <select id="class-select" v-model="selectedClass" class="filter-select">
          <option value="all">全部班级</option>
          <option v-for="(classItem, index) in classList" :key="index" :value="classItem">
            {{ classItem }}
          </option>
        </select>
      </div>
      
      <div class="filter-item">
        <label for="status-select">状态：</label>
        <select id="status-select" v-model="selectedStatus" class="filter-select">
          <option value="all">全部状态</option>
          <option v-for="(status, index) in statusList" :key="index" :value="status.value">
            {{ status.label }}
          </option>
        </select>
      </div>
      
      <div class="stats">
        已签到：{{ signedCount }} / 应签到：{{ totalCount }}
      </div>
    </div>
    
    <!-- 签到表格区域 -->
    <div class="sign-in-table">
      <table>
        <thead>
          <tr>
            <th>序号</th>
            <th>姓名</th>
            <th>学号</th>
            <th>状态</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(student, index) in filteredStudents" :key="student.id">
            <td>{{ index + 1 }}</td>
            <td>{{ student.name }}</td>
            <td>{{ student.studentId }}</td>
            <td>
              <select v-model="student.status" @change="updateStatus(student)" class="status-select">
                <option v-for="status in statusList" :key="status.value" :value="status.value">
                  {{ status.label }}
                </option>
              </select>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 模拟数据
const signInName = ref('2023年秋季学期第一次班会签到') // 签到名称
const classList = ref(['计算机2101', '计算机2102', '计算机2103'])
const statusList = ref([
  { value: 'signed', label: '已签到' },
  { value: 'unsigned', label: '未签到' },
  { value: 'late', label: '迟到' },
  { value: 'leave', label: '请假' }
])

const students = ref([
  { id: 1, name: '李秋妹', studentId: '2200470105', class: '计算机2101', status: 'unsigned' },
  { id: 2, name: '张三', studentId: '2200470106', class: '计算机2101', status: 'signed' },
  { id: 3, name: '李四', studentId: '2200470107', class: '计算机2102', status: 'late' },
  { id: 4, name: '王五', studentId: '2200470108', class: '计算机2102', status: 'leave' },
  { id: 5, name: '赵六', studentId: '2200470109', class: '计算机2103', status: 'signed' }
])

const selectedClass = ref('all')
const selectedStatus = ref('all')

// 计算属性
const filteredStudents = computed(() => {
  return students.value.filter(student => {
    const classMatch = selectedClass.value === 'all' || student.class === selectedClass.value
    const statusMatch = selectedStatus.value === 'all' || student.status === selectedStatus.value
    return classMatch && statusMatch
  })
})

const signedCount = computed(() => {
  return students.value.filter(s => s.status === 'signed').length
})

const totalCount = computed(() => {
  return students.value.length
})

// 方法
const updateStatus = (student) => {
  console.log(`更新学生 ${student.name} 的状态为 ${student.status}`)
  // 这里可以添加API调用或其他逻辑
}

const goBack = () => {
  router.go(-1) // 返回上一页
}

onMounted(() => {
  // 可以在这里加载实际数据
  // 从路由参数获取签到ID并加载对应数据
  // const signInId = route.params.id
})
</script>

<style scoped lang="scss">
.sign-in-detail-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  height:97%;
}

.header-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    background-color: #e6e6e6;
    border-color: #bfbfbf;
  }
  
  svg {
    margin-right: 4px;
  }
}

.sign-in-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
  
  .filter-item {
    display: flex;
    align-items: center;
  }
  
  .filter-select {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background-color: #fff;
    transition: all 0.3s;
    
    &:hover {
      border-color: #40a9ff;
    }
    
    &:focus {
      outline: none;
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
  
  .stats {
    margin-left: auto;
    color: #666;
    font-size: 14px;
  }
}

.sign-in-table {
  width: 100%;
  overflow-x: auto;
  
  table {
    width: 100%;
    border-collapse: collapse;
    
    th, td {
      padding: 12px 16px;
      text-align: left;
      border-bottom: 1px solid #f0f0f0;
    }
    
    th {
      background-color: #fafafa;
      font-weight: 500;
      color: #333;
    }
    
    tr:hover {
      background-color: #fafafa;
    }
  }
}

.status-select {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s;
  
  &:hover {
    border-color: #40a9ff;
  }
  
  &:focus {
    outline: none;
    border-color: #40a9ff;
  }
}
</style>