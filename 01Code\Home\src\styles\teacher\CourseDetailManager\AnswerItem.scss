//
@use "@/styles/variables.scss" as *;

.answer-item {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #e5e5e5;

  &:last-child {
    border-bottom: none;
  }

  .avatar-container {
    position: relative;
    width: 48px;
    height: 48px; 
    flex-shrink: 0; 
    margin-right: 12px;

    .avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      object-fit: cover; 
      background-color: #f0f2f5;
      display: block; 
    }

    .teacher-badge {
      position: absolute;
      right: -3px;
      bottom: -3px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: $primary-color; 
      color: white;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      border: 2px solid white;
    }
  }

  .answer-content {
    flex: 1;

    .answer-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .answer-meta {
        .answer-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-right: 8px;
        }

        .answer-school {
          font-size: 12px;
          color: #999;
        }
      }

      .answer-time {
        font-size: 12px;
        color: #999;
      }
    }

    .answer-text {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 12px;
      color: #333;
    }

    .answer-operation {
      display: flex;
      justify-content: space-between; // 添加这行使左右两侧分开
      align-items: center;
      gap: 12px;

      .action-left {
        display: flex;
        gap: 12px;
      }

      .action-right {
        display: flex;
        gap: 12px;
      }

      .like-btn {
        font-size: 14px;
        background-color: transparent;
        border: none;
        color: #999;
        display: flex;
        align-items: center;

        .iconfont {
          margin-right: 4px;
        }

        img {
          width: 14px;
          height: 14px;
        }

        &:hover {
          color: $primary-color; 
        }

        &:hover img {
          content: url('@/assets/img/General/icon-like-actived.png');
        }

        &.liked {
          color: $primary-color; 
        }

        &.liked img {
          content: url('@/assets/img/General/icon-like-actived.png');
        }
      }

      .comment-btn {
        font-size: 14px;
        background-color: transparent;
        border: none;
        color: #999;
        display: flex;
        align-items: center;

        .iconfont {
          margin-right: 4px;
        }

        img {
          width: 14px;
          height: 14px;
        }

        &:hover {
          color: $primary-color; 
        }

        &:hover img {
          content: url('@/assets/img/General/icon-reply-hover.png');
        }
      }

      .star-bth {
        display: flex;
        align-items: center;
        gap: 4px;
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        font-size: 14px;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s;
        
        &:hover {
          color: $primary-color;
          
          .iconfont img {
            content: url('@/assets/img/General/icon-star-yes-hover.png');
          }
        }
        
        .iconfont {
          display: flex;
          align-items: center;
          
          img {
            width: 16px;
            height: 16px;
          }
        }
        
        &.starred {
          color: $primary-color;
          
          .iconfont img {
            content: url('@/assets/img/General/icon-star-yes-normal.png');
          }
          
          &:hover {
            .iconfont img {
              content: url('@/assets/img/General/icon-star-yes-hover.png');
            }
          }
        }
      }
      .delete-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        font-size: 14px;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s;
        
        &:hover {
          color: $primary-color;
          
          .iconfont img {
            content: url('@/assets/img/Teacher/icon-delete-hover.png');
          }
        }
        
        .iconfont {
          display: flex;
          align-items: center;
          
          img {
            width: 16px;
            height: 16px;
          }
        }
      }
    }

    .comment-list {
      margin-top: 12px;
      border-top: 1px solid #e5e5e5;
      padding-top: 12px;

      .comment-item {
        display: flex;
        width: 100%;
        margin-bottom: 12px;

        .comment-avatar-container {
          width: 32px;
          height: 32px;
          flex-shrink: 0;
          margin-right: 8px;
          overflow: hidden; 
          border-radius: 50%; 
        }

        .comment-avatar {
          width: 100%; 
          height: 100%; 
          object-fit: cover; 
          display: block; 
        }

        .comment-content {
          flex: 1;
          position: relative;

          .comment-meta {
            display: flex;
            //justify-content: space-between;
            margin-bottom: 4px;

            .comment-name {
              font-size: 14px;
              font-weight: 500;
              color: #333;
              flex: 1;
            }

            .comment-time {
              font-size: 12px;
              color: #999;
              white-space: nowrap;
            }
          }

          .comment-text {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
          }

            .comment-actions {
              display: flex;
              gap: 10px;
              
              button {
                display: flex;
                align-items: center;
                gap: 4px;
                background: none;
                border: none;
                color: #666;
                cursor: pointer;
                font-size: 12px;
                padding: 2px 6px;
                border-radius: 4px;
                
                &:hover {
                  color: $primary-color;
                }
                
                .iconfont {
                  display: flex;
                  
                  img {
                    width: 14px;
                    height: 14px;
                  }
                }
              }

              .reply-comment-btn{
                &:hover {
                  color: $primary-color;
                  .iconfont img {
                    content: url('@/assets/img/General/icon-reply2-hover.png');
                  }
                }
              }
              
              .edit-comment-btn {
                &:hover {
                  color: $primary-color;
                  .iconfont img {
                    content: url('@/assets/img/General/icon-edit-hover.png');
                  }
                }
              }
              
              .delete-comment-btn {
                &:hover {
                  color: $primary-color;
                  .iconfont img {
                    content: url('@/assets/img/Teacher/icon-delete-hover.png');
                  }
                }
              }
            }
        }
      }

      .comment-input-area {
        display: flex;
        gap: 8px;

        .comment-textarea {
          flex: 1;
          padding: 8px;
          border: 1px solid #e5e5e5;
          border-radius: 4px;
          font-size: 14px;
          color: #333;

          &:focus {
            outline: none;
            border-color: #1677ff; 
          }
        }

        .comment-submit-btn {
          padding: 8px 16px;
          border-radius: 4px;
          background-color: $primary-color; 
          border: none;
          color: #fff;
          font-size: 14px;

          &:hover {
            background-color: $button-hover; 
          }
        }
      }
    }
  }
}

.comment-pagination-container {
  margin-top: 12px;
  
  .comment-items {
    display: flex;
    margin-bottom: 12px;
  }
  
  .comment-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 16px;
    margin-bottom: 16px;
    gap: 8px;
    
    .page-btn {
      padding: 6px 12px;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      font-size: 14px;
      color: #666;
      background-color: #fff;
      cursor: pointer;
      
      &:hover:not(.disabled) {
        background-color: #f5f5f5;
      }
      
      &.disabled {
        color: #ccc;
        cursor: not-allowed;
        border-color: #eee;
        background-color: #f9f9f9;
      }
    }
    
    .page-info {
      font-size: 14px;
      color: #666;
    }
  }
}

.no-comments {
  font-size: 14px;
  color: #999;
  padding: 12px 0;
  text-align: center;
}

.comment-list {
  max-height: 500px;
  overflow-y: auto;
  transition: all 0.3s ease;
  
  .comment-item {
    margin-bottom: 12px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 在AnswerItem.vue的style部分添加
.edit-comment-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  
  .edit-comment-content {
    background: white;
    border-radius: 8px;
    width: 500px;
    max-width: 90%;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    
    .edit-comment-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      h4 {
        margin: 0;
        font-size: 16px;
      }
      
      .close-btn {
        background: none;
        border: none;
        cursor: pointer;
        padding: 5px;
      }
    }
    
    .edit-comment-body {
      margin-bottom: 15px;
      
      .edit-comment-textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        resize: vertical;
        min-height: 100px;
      }
    }
    
    .edit-comment-footer {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      
      button {
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        
        &.cancel-btn {
          background: #f5f5f5;
          border: 1px solid #ddd;
          
          &:hover {
            background: #eaeaea;
          }
        }
        
        &.submit-btn {
          background: $primary-color;
          color: white;
          border: none;
          
          &:hover {
            background: $button-hover;
          }
        }
      }
    }
  }
}