import { defineStore } from 'pinia';
import { ref, computed, onMounted, watch } from 'vue';
import { getStudentMessages, markMessageAsRead, checkReadStatus, getUserInfoById } from '@/api/student/message';
import { useUserStore } from '@/stores/userStore';
import { ElMessage } from 'element-plus';

export const useMessageStore = defineStore('message', () => {
    const messages = ref([]);
    const loading = ref(false);
    const error = ref(null);
    const total = ref(0);
    const userStore = useUserStore();
    const userId = computed(() => userStore.user.id);

    // 获取消息列表，自动补充【已读状态 + 发布者详情】
    const fetchMessages = async (pageNum = 1, pageSize = 10) => {
        loading.value = true;
        error.value = null;
        try {
            const response = await getStudentMessages({
                pageNum,
                pageSize,
                userId: userId.value,
            });

            // 批量补充：已读状态 + 发布者详情
            const messagesWithDetails = await Promise.all(
                response.map(async (msg) => {
                    const isRead = await checkReadStatus(msg.id, userId.value);
                    const publisherInfo = await getUserInfoById(msg.publisherId);
                    return {
                        ...msg,
                        read: isRead,
                        publisher: publisherInfo // 存储发布者详情（包含头像、名称等）
                    };
                })
            );
            messages.value = messagesWithDetails;
            total.value = response.total;
        } catch (err) {
            error.value = '无法加载消息，请稍后再试';
            console.error(err);
        } finally {
            loading.value = false;
        }
    };

    // 加载更多
    const loadMoreMessages = async () => {
        if (loading.value) return;
        const currentPage = Math.ceil(messages.value.length / pageSize);
        const nextPage = currentPage + 1;
        try {
            const response = await getStudentMessages({
                pageNum: nextPage,
                pageSize: pageSize,
                userId: userId.value
            });
            const newMessagesWithDetails = await Promise.all(
                response.map(async (msg) => {
                    const isRead = await checkReadStatus(msg.id, userId.value);
                    const publisherInfo = await getUserInfoById(msg.publisherId);
                    return {
                        ...msg,
                        read: isRead,
                        publisher: publisherInfo
                    };
                })
            );
            messages.value = [...messages.value, ...newMessagesWithDetails];
            total.value = response.total;
        } catch (err) {
            ElMessage.error('加载更多失败，请重试');
            console.error(err);
        }
    };

    // 标记消息为已读（原逻辑不变）
    const markAsRead = async (noticeId) => {
        try {
            await markMessageAsRead(noticeId, userId.value);
            messages.value = messages.value.map(msg =>
                msg.id === noticeId ? { ...msg, read: true } : msg
            );
            ElMessage.success('消息已标记为已读');
        } catch (error) {
            ElMessage.error('标记消息为已读失败: ' + (error.message || '请重试'));
        }
    };

    return {
        messages,
        loading,
        error,
        total,
        fetchMessages,
        loadMoreMessages,
        markAsRead
    };
});