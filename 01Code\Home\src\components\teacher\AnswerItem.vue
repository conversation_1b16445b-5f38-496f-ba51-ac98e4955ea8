<!--src/components/teacher/AnswerItem.vue-->
<template>
  <div class="answer-item">
    <div class="avatar-container">
      <img :src="answer.avatar || '@/assets/default-avatar.png'" alt="回答者头像" class="avatar" />
      <div v-if="answer.role === 2" class="teacher-badge">师</div>
    </div>
    <div class="answer-content">
      <div class="answer-header">                                           
        <div class="answer-meta">
          <span class="answer-name">{{ answer.publisher }}</span>
          <span class="answer-school">{{ answer.school }}</span>
        </div>
        <span class="answer-time">{{ answer.updateTimeFormatted || '刚刚' }}</span>
      </div>
      <div class="answer-text" v-html="answer.content"></div>
      <div class="answer-operation">
  <div class="action-left">
    <button class="like-btn" :class="{ liked: answer.isLiked }" @click="handleLike">
      <i class="iconfont icon-like">
        <img src="@/assets/img/General/icon-like-normal.png" alt="点赞图标">
      </i>
      <span>{{ answer.likeCount || 0 }}个赞</span>
    </button>

    <button class="comment-btn" @click="toggleCommentSection">
      <i class="iconfont icon-comment">
        <img src="@/assets/img/General/icon-reply-normal.png" alt="评论图标">
      </i>
      <span>{{ answer.commentCount || 0 }} 条回复</span>
    </button>
  </div>
  
  <div class="action-right">
    <button class="star-bth" :class="{ starred: answer.isStarred }">
      <i class="iconfont icon-favorite">
        <img src="@/assets/img/General/icon-star-yes-normal.png" alt="收藏图标">
      </i>
      <span>{{ answer.isStarred ? '已收藏' : '收藏' }}</span>
    </button>
    
    <button class="delete-btn">
      <i class="iconfont icon-delete">
        <img src="@/assets/img/Teacher/icon-delete-normal.png" alt="删除图标">
      </i>
      <span>删除</span>
    </button>
  </div>
</div>
      
      <div class="comment-list" v-show="answer.showComments">
        <div v-if="answer.comments.length === 0" class="no-comments">暂无评论</div>
        
        <div v-if="answer.comments.length > 0">
          <div class="comment-pagination-container">
            <div class="comment-items" v-for="(comment, commentIndex) in getPagedComments(answer, currentPage)" :key="comment.id || commentIndex">
              <div class="comment-item">
                <div class="comment-avatar-container">
                  <img :src="comment.avatar || '@/assets/default-avatar.png'" alt="评论者头像" class="comment-avatar" />
                </div>
                <div class="comment-content">
                  <div class="comment-meta">
                    <span class="comment-name">{{ comment.name || comment.userInfo?.name || '匿名用户' }}</span>
                    <span class="comment-time">{{ comment.updateTimeFormatted || '刚刚' }}</span>
                  </div>
                  <div class="comment-text" v-html="comment.content"></div>
                  <div class="comment-actions">
                    <button class="reply-comment-btn" @click="handleReplyComment(comment)">
                      <i class="iconfont icon-reply">
                        <img src="@/assets/img/General/icon-reply2-normal.png" alt="二级回复图标">
                      </i>
                      <span>回复</span>
                    </button>
                    <button 
                      v-if="comment.commenterId === authStore.user.id"
                      class="edit-comment-btn" 
                      @click="handleEditComment(comment)"
                    >
                      <i class="iconfont icon-edit">
                        <img src="@/assets/img/General/icon-edit-normal.png" alt="编辑图标">
                      </i>
                      <span>编辑</span>
                    </button>
                    <button class="delete-comment-btn" @click="handleDeleteComment(comment)">
                      <i class="iconfont icon-delete">
                        <img src="@/assets/img/Teacher/icon-delete-normal.png" alt="删除图标">
                      </i>
                      <span>删除</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div v-if="getTotalPages(answer) > 1" class="comment-pagination">
              <button 
                class="page-btn" 
                :class="{ disabled: currentPage === 1 }" 
                @click="changeCommentPage(1)"
              >
                首页
              </button>
              <button 
                class="page-btn" 
                :class="{ disabled: currentPage === 1 }" 
                @click="changeCommentPage(currentPage - 1)"
              >
                上一页
              </button>
              <span class="page-info">
                第 {{ currentPage }} 页 / 共 {{ getTotalPages(answer) }} 页
              </span>
              <button 
                class="page-btn" 
                :class="{ disabled: currentPage === getTotalPages(answer) }" 
                @click="changeCommentPage(currentPage + 1)"
              >
                下一页
              </button>
              <button 
                class="page-btn" 
                :class="{ disabled: currentPage === getTotalPages(answer) }" 
                @click="changeCommentPage(getTotalPages(answer))"
              >
                末页
              </button>
            </div>
          </div>
        </div>
        
        <div class="comment-input-area">
          <textarea 
            class="comment-textarea" 
            v-model="answer.commentInput" 
            placeholder="发表评论..." 
            rows="2"
          ></textarea>
          <button 
            class="comment-submit-btn" 
            @click="submitComment"
            :disabled="isSubmittingComment"
          >
            {{ isSubmittingComment ? '提交中...' : '评论' }}
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- 编辑模板 -->
  <div class="edit-comment-modal" v-if="showEditPanel">
    <div class="edit-comment-content">
      <div class="edit-comment-header">
        <h4>编辑评论</h4>
        <button class="close-btn" @click="showEditPanel = false">
          <i class="iconfont icon-close">
            <img src="@/assets/img/General/icon-close.png" alt="关闭">
          </i>
        </button>
      </div>
      <div class="edit-comment-body">
        <textarea 
          class="edit-comment-textarea" 
          v-model="editContent" 
          rows="4"
        ></textarea>
      </div>
      <div class="edit-comment-footer">
        <button class="cancel-btn" @click="showEditPanel = false">取消</button>
        <button class="submit-btn" @click="submitEditComment">保存</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth/auth'
import { useRoute } from 'vue-router'
import { saveComment, getUserDetail,likeComment, removeLike,updateComment  } from '@/api/teacher/discuss'
import { formatTime } from '@/utils/dateUtils'

const props = defineProps({
  answer: {
    type: Object,
    required: true
  },
  commentsPerPage: {
    type: Number,
    default: 5
  }
})

const emit = defineEmits([
  'like', 
  'toggle-comment', 
  'submit-comment', 
  'reply-comment',
  'delete-comment',
  'edit-comment'
])

const authStore = useAuthStore()
const route = useRoute()
const currentPage = ref(1)
const isSubmittingComment = ref(false)

const userInfoMap = ref(new Map())
const userInfoCache = new Map()

// 获取分页后的评论
const getPagedComments = (answer, page) => {
  // 如果不需要对二级评论分页，直接返回全部
  return answer.comments.map(comment => ({
    ...comment,
    name: comment.name || comment.userInfo?.name || '匿名用户',
    avatar: comment.avatar || comment.userInfo?.avatar || defaultAvatar
  }))
}

// 计算总页数 - 如果不需要分页，直接返回1
const getTotalPages = (answer) => {
  return 1 // 或者根据需求返回 Math.ceil(answer.comments.length / commentsPerPage)
}

// 切换评论页码
const changeCommentPage = (page) => {
  const totalPages = getTotalPages(props.answer)
  if (page < 1 || page > totalPages) return
  currentPage.value = page
}

// 获取用户信息
const fetchUserInfo = async (userId) => {
  if (userInfoCache.has(userId)) {
    return userInfoCache.get(userId)
  }
  
  try {
    const res = await getUserDetail(userId)
    if (res.code === 200) {
      userInfoCache.set(userId, res.result)
      return res.result
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    return null
  }
}

// 处理挂载时获取二级评论用户信息
onMounted(async () => {
  if (props.answer.comments.length > 0) {
    for (const comment of props.answer.comments) {
      // 处理一级评论的用户信息
      if (comment.commenterId && !userInfoMap.value.has(comment.commenterId)) {
        await fetchUserInfo(comment.commenterId, comment)
      }
      
      // 处理二级评论的用户信息
      if (comment.children && comment.children.length > 0) {
        for (const childComment of comment.children) {
          if (childComment.commenterId && !userInfoMap.value.has(childComment.commenterId)) {
            await fetchUserInfo(childComment.commenterId, childComment)
          }
        }
      }
    }
  }
})

// 点赞处理
const handleLike = async () => {
  try {
    // 如果已经点赞，则取消点赞
    if (props.answer.isLiked) {
      // 这里需要知道点赞记录的ID，可以在获取评论数据时返回
      const res = await removeLike(props.answer.likeId)
      if (res.code === 200) {
        emit('like', {
          answer: props.answer,
          action: 'unlike',
          likeCount: props.answer.likeCount - 1,
          likeId: null
        })
      }
    } else {
      // 未点赞则进行点赞
      const res = await likeComment(props.answer.id)
      if (res.code === 200) {
        emit('like', {
          answer: props.answer,
          action: 'like',
          likeCount: props.answer.likeCount + 1,
          likeId: res.result.id // 保存点赞ID，用于取消点赞
        })
      }
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    // 可以在这里添加错误提示
  }
}

// 切换评论区显示/隐藏
const toggleCommentSection = () => emit('toggle-comment', props.answer)

// 提交评论
const submitComment = async () => {
  if (!props.answer.commentInput.trim()) {
    emit('submit-comment', { 
      answer: props.answer,
      error: '评论内容不能为空'
    })
    return
  }

  isSubmittingComment.value = true

  try {
    const commentData = {
      postId: props.answer.postId || route.params.topicId,
      commenterId: authStore.user.id,
      content: props.answer.commentInput,
      publishTime: Date.now(),
      parentId: props.answer.id // 确保这是后端返回的真实ID
    }

    const res = await saveComment(commentData)
    
    if (res.code === 200) {
      // 构建新评论对象
      const newComment = {
        id: res.result.id,
        commenterId: authStore.user.id,
        content: props.answer.commentInput,
        publishTime: res.result.publishTime,
        updateTimeFormatted: formatTime(res.result.publishTime),
        name: authStore.user.name,
        avatar: authStore.user.avatar,
        school: authStore.user.institution,
        role: authStore.user.role
      }

      // 触发事件通知父组件
      emit('submit-comment', {
        answer: props.answer,
        newComment: newComment
      })
      
      // 清空输入框
      props.answer.commentInput = ''
      
      // 自动展开评论区域（如果已关闭）
      props.answer.showComments = true
      
      // 滚动到最新评论
      nextTick(() => {
        const commentsContainer = document.querySelector(`.answer-item[data-id="${props.answer.id}"] .comment-list`)
        if (commentsContainer) {
          commentsContainer.scrollTop = commentsContainer.scrollHeight
        }
      })
    } else {
      throw new Error(res.msg || '评论提交失败')
    }
  } catch (error) {
    console.error('评论提交错误:', error)
    emit('submit-comment', {
      answer: props.answer,
      error: error.message
    })
  } finally {
    isSubmittingComment.value = false
  }
}

// 回复评论
const handleReplyComment = (comment) => {
  props.answer.commentInput = `回复 @${comment.name}: `
  nextTick(() => {
    const textarea = document.querySelector(`.answer-item[data-id="${props.answer.id}"] .comment-textarea`)
    if (textarea) {
      textarea.focus()
      textarea.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  })
}

// 添加删除评论方法
const handleDeleteComment = (comment) => {
  emit('delete-comment', {
    answer: props.answer,
    comment: comment
  })
}


const showEditPanel = ref(false)
const editingComment = ref(null)
const editContent = ref('')

// 打开编辑面板
const handleEditComment = (comment) => {
  console.log('【编辑评论数据】', {
    commentId: comment.id,
    originalContent: comment.content
  });
  editingComment.value = comment;
  editContent.value = comment.content;
  showEditPanel.value = true;
}

// 提交编辑
const submitEditComment = async () => {
  console.log('【编辑评论请求参数】', {
    id: editingComment.value.id,
    content: editContent.value,
    // 其他参数...
  });
  console.groupCollapsed('===== 开始调试评论编辑功能 =====');
  console.log('1. 进入submitEditComment方法');
  
  if (!editContent.value.trim()) {
    console.log('2. 编辑内容为空，触发错误提示');
    emit('edit-comment', {
      error: '评论内容不能为空'
    });
    console.groupEnd();
    return;
  }
  
  console.log('3. 编辑内容验证通过，内容为:', editContent.value);
  
  try {
    // 假设这里有updateComment方法，先确认参数是否正确
    const commentData = {
      id: editingComment.value.id,
      postId: editingComment.value.postId || route.params.topicId,
      commenterId: authStore.user.id,
      content: editContent.value,
      publishTime: Date.now(),
      parentId: editingComment.value.parentId || null
    };
    
    console.log('4. 准备调用updateComment接口，参数为:', commentData);
    
    // 这里假设updateComment是从api引入的方法，先模拟接口调用
    // const res = await updateComment(commentData);
    
    // 临时使用模拟数据进行调试
    const res = {
      code: 200, // 假设接口调用成功，调试时可修改此值模拟失败情况
      message: '更新成功',
      result: {
        id: commentData.id,
        content: commentData.content,
        publishTime: commentData.publishTime
      }
    };
    
    console.log('5. 接口返回结果:', res);
    
    if (res.code === 200) {
      console.log('6. 接口调用成功，准备更新UI');
      showEditPanel.value = false;
      emit('edit-comment', {
        comment: editingComment.value,
        updatedComment: {
          ...editingComment.value,
          content: editContent.value,
          updateTimeFormatted: formatTime(Date.now())
        }
      });
      console.log('7. 触发edit-comment事件，传递更新后的数据');
    } else {
      console.error('8. 接口调用失败，错误信息:', res.message || '未知错误');
      emit('edit-comment', {
        error: res.message || '更新评论失败'
      });
    }
  } catch (error) {
    console.error('9. 调用接口时发生异常:', error);
    if (error.response) {
      console.error('9.1 响应数据:', error.response.data);
      console.error('9.2 响应状态:', error.response.status);
      console.error('9.3 响应头:', error.response.headers);
    } else if (error.request) {
      console.error('9.4 请求发送失败，未收到响应:', error.request);
    } else {
      console.error('9.5 错误发生在设置请求时:', error.message);
    }
    emit('edit-comment', {
      error: error.message || '更新评论时发生错误'
    });
  } finally {
    console.groupEnd();
  }
}
</script>

<style scoped lang="scss">
@use "@/styles/teacher/CourseDetailManager/AnswerItem";

.answer-operation {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .star-bth, .delete-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s;
    
    &:hover {
      background-color: #f5f5f5;
      color: #333;
    }
    
    .iconfont {
      display: flex;
      align-items: center;
      
      img {
        width: 16px;
        height: 16px;
      }
    }
  }
  
  .delete-btn {
    color: #ff4d4f;
    
    &:hover {
      background-color: #fff2f0;
    }
  }
}
</style>