<!--src\views\teacher\CourseDetailManager\Q-A\TopicDiscuss.vue-->
<template>
  <div class="topic-discuss">
    <!-- 在filter-header中添加批量操作按钮 -->
    <div class="filter-header">
      <div class="tab-header">
        <button 
          v-for="(tab, index) in tabs" 
          :key="index" 
          @click="handleTabChange(tab.value)"
          :class="{ active: activeTab === tab.value }"
        >
          {{ tab.label }}
        </button>
      </div>

      <div class="batch-actions" v-if="selectedPosts.length > 0">
        <button 
          class="btn delete-btn"
          @click="handleBatchDelete"
          :loading="batchDeleting"
        >
          删除选中({{ selectedPosts.length }})
        </button>
        <button class="btn clear-btn" @click="clearSelection">取消选择</button>
      </div>
      
      <el-select 
        v-model="statusFilter" 
        placeholder="审核状态" 
        class="status-filter"
        @change="handleStatusFilterChange"
        clearable
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>

    <!-- 话题列表 - 添加复选框 -->
    <!-- 话题列表 - 修改后的复选框部分 -->
    <div class="topic-list">
      <div 
        v-for="(item, index) in topicList" 
        :key="index" 
        class="topic-item"
        :class="{ 'selected-item': selectedPostIds.includes(item.id) }"
      >
        <input 
          type="checkbox" 
          v-model="selectedPostIds" 
          :value="item.id"
          class="topic-checkbox"
        >
        
        <div 
          class="topic-content"
          @click="openDiscussDetail(item.id)"
        >
          <div class="topic-title">
            <span v-if="item.isPinned" class="pinned-tag">【置顶】</span>
            {{ item.title }}
          </div>
          <div class="topic-meta">
            <span>{{ item.viewCount || 0 }} 人浏览</span>
            <span>{{ item.answerCount || 0 }} 条回答</span>
            <div class="author-info">
              <div class="avatar-container">
                <img 
                  class="avatar" 
                  :src="item.user?.avatar || defaultAvatar" 
                  :alt="item.user?.name || '用户头像'"
                >
                <div v-if="item.user?.role === 2" class="teacher-badge">师</div>
              </div>
              <span>{{ item.user?.name || item.user?.account || '匿名用户' }}</span>
            </div>
            <span>{{ formatAgo(item.publishTime) }}更新</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页组件 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-if="total > 0"
        class="pagination"
        background
        layout="prev, pager, next"
        :current-page="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="total"
        @current-change="handlePageChange"
      />
    </div>
    
    <!-- 加载状态 -->
    <div v-if="!loading && topicList.length === 0" class="empty">暂无话题</div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth/auth'
import { getTeacherPosts, getUserDetail, getStarredPosts,getStarDetail,batchDeletePosts } from '@/api/teacher/discuss'
import { formatAgo } from '@/utils/dateUtils'

const route = useRoute()
const router = useRouter()
const hoverTitle = ref(null)
const authStore = useAuthStore()

// 新增的状态和变量
const selectedPostIds = ref([]) // 选中的话题ID数组
const batchDeleting = ref(false) // 批量删除加载状态

// 审核状态选项
const statusOptions = ref([
  { value: 0, label: '未审核' },
  { value: 1, label: '已通过' },
  { value: 2, label: '未通过' }
])
const statusFilter = ref(null)

// 标签数据 - 修改为"最新"和"收藏"
const tabs = ref([
  { label: '最新', value: 'latest' },
  { label: '收藏', value: 'starred' }
])

// 激活的标签
const activeTab = ref('latest')
// 话题列表
const topicList = ref([])
// 加载状态
const loading = ref(false)
// 分页数据
const pagination = ref({
  pageNum: 1,
  pageSize: 5
})
// 总数
const total = ref(0)

const courseId = route.params.courseId

// 获取状态标签文本
const getStatusText = (status) => {
  switch (status) {
    case 0: return '未审核'
    case 1: return '已通过'
    case 2: return '未通过'
    default: return '未知状态'
  }
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 0: return 'warning'
    case 1: return 'success'
    case 2: return 'danger'
    default: return 'info'
  }
}


// 获取话题列表
const fetchTopics = async () => {
  try {
    loading.value = true
    topicList.value = []
    
    let res
    if (activeTab.value === 'latest') {
      // 最新列表逻辑
      const params = {
        ...pagination.value,
        courseId: route.params.courseId,
        status: statusFilter.value !== null ? statusFilter.value : undefined,
        withCommentCount: true
      }
      res = await getTeacherPosts(params)
      
      if (res.code === 200) {
        // 获取用户信息
        const topicsWithUsers = await Promise.all(
          res.result.records.map(async topic => {
            if (!topic.publisherId) return { ...topic, user: null }
            
            try {
              const userRes = await getUserDetail(topic.publisherId)
              return {
                ...topic,
                user: userRes?.code === 200 ? userRes.result : null,
                answerCount: topic.commentCount || 0
              }
            } catch (error) {
              console.error('获取用户信息失败:', error)
              return { ...topic, user: null, answerCount: topic.commentCount || 0 }
            }
          })
        )
        
        res.result.records = topicsWithUsers
      }
    } else {
      // 收藏列表逻辑
      const params = {
        ...pagination.value,
        userId: authStore.user.id,
        courseId: route.params.courseId
      }
      res = await getStarredPosts(params)
      
      if (res.code === 200 && res.result.records.length > 0) {
        const postDetails = await Promise.all(
          res.result.records.map(async record => {
            try {
              const postRes = await getPostDetail(record.postId)
              if (postRes.code === 200) {
                return {
                  ...postRes.result,
                  isStarred: true,
                  id: record.postId
                }
              }
              return null
            } catch (error) {
              console.error('获取帖子详情失败:', error)
              return null
            }
          })
        )
        
        const validPosts = postDetails.filter(post => post !== null)
        const postsWithUsers = await Promise.all(
          validPosts.map(async post => {
            if (!post.publisherId) return { ...post, user: null }
            
            try {
              const userRes = await getUserDetail(post.publisherId)
              return {
                ...post,
                user: userRes?.code === 200 ? userRes.result : null,
                answerCount: post.commentCount || 0
              }
            } catch (error) {
              return { ...post, user: null, answerCount: 0 }
            }
          })
        )
        
        res.result.records = postsWithUsers
      }
    }
    
    if (res.code === 200) {
      topicList.value = res.result.records.sort((a, b) => b.isPinned - a.isPinned)
      total.value = res.result.total
    }
  } catch (error) {
    console.error('获取话题列表失败:', error)
  } finally {
    loading.value = false
  }
}


// 状态筛选变化
const handleStatusFilterChange = () => {
  pagination.value.pageNum = 1
  fetchTopics()
}
// 暴露给父窗口的方法
const updateStarredList = () => {
  if (activeTab.value === 'starred') {
    fetchTopics()
  }
}

async function getPostDetail(postId) {
  const res = await getTeacherPosts({
    pageNum: 1,
    pageSize: 1,
    postId: postId,
    withCommentCount: true
  })
  
  if (res.code === 200 && res.result.records.length > 0) {
    return {
      code: 200,
      result: res.result.records[0]
    }
  }
  
  return { code: 404, result: null }
}


// 标签切换
const handleTabChange = (tab) => {
  activeTab.value = tab
  pagination.value.pageNum = 1
  fetchTopics()
}

// 分页变化
const handlePageChange = (page) => {
  pagination.value.pageNum = page
  fetchTopics()
}

const openDiscussDetail = (topicId) => {
  const routeData = router.resolve({
    name: 'DiscussDetail',
    params: {
      courseId: courseId,
      topicId: topicId
    }
  })
  window.open(routeData.href, '_blank')
}

// 计算选中的话题对象
const selectedPosts = computed(() => {
  return topicList.value.filter(item => selectedPostIds.value.includes(item.id))
})

// 处理选择变化
const handleSelectChange = (postId) => {
  // 这里不需要额外处理，因为selectedPostIds已经是响应式的
}

// 清空选择
const clearSelection = () => {
  selectedPostIds.value = []
}

// 批量删除处理
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedPosts.value.length} 个话题吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    batchDeleting.value = true
    const res = await batchDeletePosts(selectedPostIds.value)
    
    if (res.code === 200) {
      ElMessage.success(res.msg || '删除成功')
      clearSelection()
      fetchTopics() // 重新加载列表
    } else {
      ElMessage.error(res.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  } finally {
    batchDeleting.value = false
  }
}

// 初始化获取数据
onMounted(() => {
  fetchTopics()
})

// 暴露方法给window
defineExpose({
  courseId,
  fetchTopics,
  updateStarredList // 添加这行
})
</script>

<style lang="scss" scoped>
.topic-discuss {
  margin-bottom: 20px;
  padding: 30px;
  background-color: white;
  height: 80vh;
  position: relative;
  display: flex;
  flex-direction: column;

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .tab-header {
      display: flex;
      gap: 10px;
    }

    .status-filter {
      width: 120px;
    }
  }
/*
  .topic-item {
    .topic-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 8px;

      .topic-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .status-tag {
        margin-left: auto;
      }
    }
  }
    */

  .tab-header {
    display: flex;
    gap: 10px;

    button {
      padding: 8px 16px;
      border: none;
      background: transparent;
      cursor: pointer;
      color: #666;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        color: #333;
      }

      &.active {
        color: #333;
      }

      &.active::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 3px;
        background-color: $primary-color;
      }
    }
  }

  .topic-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
    flex: 1;

    .topic-item {
      border:none;
      border-bottom: 1px solid #e5e7eb;
      padding: 16px 12px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: none; /* 移除悬浮阴影 */
        background-color: #f9fafb; /* 改为悬浮背景色变化 */
      }

      .topic-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #333;
      }

      .topic-meta {
        font-size: 12px;
        color: #909399;
        display: flex;
        align-items: center;

        & > *:not(:last-child) {
          margin-right: 30px;
        }

        .author-info {
          display: flex;
          align-items: center;
          gap: 8px;
          white-space: nowrap;
          flex: 1;

          .avatar-container {
            position: relative;
            width: 24px;
            height: 24px;
            
            .avatar {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              object-fit: cover;
              background-color: #f0f2f5;
            }

            .teacher-badge {
              position: absolute;
              right: -3px;
              bottom: -3px;
              width: 16px;
              height: 16px;
              border-radius: 50%;
              background-color: $primary-color;
              color: white;
              font-size: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              line-height: 1;
              border: 2px solid white;
            }
          }
        }

        .update-time {
          margin-left: auto; /* 这将把更新时间推到最右侧 */
        }
      }
    }
  }

  .pagination-wrapper {
    background: white; /* 可选 - 背景色与容器一致 */
    position: sticky; /* 关键样式 */
    bottom: 0; /* 关键样式 */
    z-index: 1; /* 确保在内容上方 */

      .pagination {
      margin-top: 20px;
      justify-content: center;
    }

  }
  
  .empty {
    text-align: center;
    padding: 20px;
    color: #999;
  }
}

.pinned-tag {
  color: #ff4d4f;
  margin-right: 6px;
  font-weight: bold;
  font-size: 0.9em;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .batch-actions {
    margin-left: auto;
    padding-right: 20px;

    .btn{
      background-color: white;
      color: $text-color-light;
      font-size: 0.9rem;
      padding: 4px 12px;
      border: $course-tabs solid 1px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background-color: $course-tabs;
        color: $text-color;
      }
    }

    .delete-btn {
      border-radius: 20px 0px 0px 20px;
    }
   .clear-btn {
      border-radius: 0px 20px 20px 0px;
    }
  }
}

.topic-item {
  display: flex;
  align-items: flex-start; /* 顶部对齐 */
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.3s;
  
  &.selected-item {
    background-color: #f5f7fa;
    border-left: 3px solid $primary-color;
  }
  
  .topic-checkbox {
    margin-right: 12px;
    margin-top: 3px; /* 与标题对齐 */
    width: 16px;
    height: 16px;
    cursor: pointer;
    
    /* 自定义复选框样式 */
    appearance: none;
    -webkit-appearance: none;
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 3px;
    position: relative;
    transition: all 0.2s ease;
    
    &:checked {
      background-color: $primary-color;
      border-color: $primary-color;
      transition: all 0.2s ease;
      
      &::after {
        content: "";
        position: absolute;
        left: 4px;
        top: 1px;
        width: 5px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
      }
    }
    
    &:hover:not(:checked) {
      border-color: $primary-color;
    }
  }
  
  .topic-content {
    flex: 1;
    cursor: pointer;
    
    .topic-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;
      color: #333;
      
      &:hover {
        color: $primary-color;
      }
    }
  }
}
</style>