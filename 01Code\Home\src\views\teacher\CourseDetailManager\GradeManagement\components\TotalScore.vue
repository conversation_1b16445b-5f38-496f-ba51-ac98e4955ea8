<template>
  <div class="score-container">
    <!-- 标题和操作按钮 -->
    <div class="header flex justify-between items-center mb-4">
      <h1 class="text-xl font-bold">总成绩</h1>
      <div class="flex space-x-2">
        <el-button type="primary" :icon="Download" @click="handleDownload">
          下载总成绩
        </el-button>
        <el-button type="primary" :icon="Setting" @click="handleWeightSettings">
          成绩加权设置
        </el-button>
        <el-button type="primary" :icon="Box" @click="handleArchive">
          成绩归档封存
        </el-button>
      </div>
    </div>

    <!-- 专业名称和图表标题 -->
    <div class="text-lg font-medium mb-2">数字媒体技术</div>
    <div class="text-md font-medium mb-4">总成绩分布</div>

    <!-- 统计数据 -->
    <div class="flex justify-between items-center mb-4">
      <div></div>
      <div class="flex space-x-6">
        <div class="flex items-center">
          <span class="text-gray-600 mr-2">平均分：</span>
          <span class="font-semibold">{{ averageScore.toFixed(1) }}</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-600 mr-2">最高分：</span>
          <span class="font-semibold">{{ maxScore }}</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-600 mr-2">最低分：</span>
          <span class="font-semibold">{{ minScore }}</span>
        </div>
      </div>
    </div>

    <!-- 成绩分布图 -->
    <div class="chart-container">
      <div ref="chartRef" class="w-full h-[400px]"></div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { ElButton } from 'element-plus';
import { Download, Setting, Box } from '@element-plus/icons-vue';
import 'element-plus/dist/index.css';

export default defineComponent({
  name: 'ScoreStatistics',
  components: {
    ElButton,
    Download,
    Setting,
    Box
  },
  setup() {
    const chartRef = ref(null);
    const chartInstance = ref(null);
    const averageScore = ref(0);
    const maxScore = ref(0);
    const minScore = ref(0);
    const scoreData = ref([]);

    // 生成模拟成绩数据 - 简化版，使用固定数据
    const generateScoreData = () => {
      // 固定数据示例 - 150个学生的成绩分布
      return [
        45, 52, 38, 61, 55, 68, 72, 65, 58, 63,
        78, 82, 75, 85, 88, 92, 95, 89, 76, 81,
        67, 59, 62, 71, 74, 77, 83, 86, 91, 94,
        50, 53, 57, 60, 64, 69, 73, 79, 84, 87,
        90, 93, 96, 98, 100, 97, 99, 80, 70, 66,
        56, 54, 51, 49, 47, 44, 42, 39, 37, 35,
        48, 46, 43, 41, 40, 36, 34, 33, 32, 31,
        30, 29, 28, 27, 26, 25, 24, 23, 22, 21,
        20, 19, 18, 17, 16, 15, 14, 13, 12, 11,
        10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0,
        65, 68, 72, 75, 78, 82, 85, 88, 92, 95,
        60, 62, 64, 66, 70, 74, 76, 80, 84, 86,
        55, 58, 63, 67, 71, 73, 77, 81, 83, 87,
        50, 52, 54, 56, 59, 61, 65, 69, 72, 75,
        40, 42, 44, 46, 48, 51, 53, 57, 60, 63
      ];
    };

    // 生成图表数据 - 简化版
    const generateChartData = () => {
      const data = generateScoreData();
      scoreData.value = data;
      
      // 更新统计数据
      averageScore.value = data.reduce((sum, score) => sum + score, 0) / data.length;
      maxScore.value = Math.max(...data);
      minScore.value = Math.min(...data);
      
      // 创建直方图数据 - 10个分数段
      const bins = Array(10).fill(0);
      data.forEach(score => {
        const binIndex = Math.min(Math.floor(score / 10), 9);
        bins[binIndex]++;
      });
      
      return {
        bins,
        xAxisLabels: ['0-10', '11-20', '21-30', '31-40', '41-50', '51-60', '61-70', '71-80', '81-90', '91-100']
      };
    };

    // 初始化图表 - 简化版
    const initChart = () => {
      if (!chartRef.value) return;
      
      chartInstance.value = echarts.init(chartRef.value);
      const { bins, xAxisLabels } = generateChartData();
      
      chartInstance.value.setOption({
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}人'
        },
        xAxis: {
          type: 'category',
          data: xAxisLabels
        },
        yAxis: {
          type: 'value',
          name: '学生人数'
        },
        series: [{
          data: bins,
          type: 'bar',
          itemStyle: {
            color: '#409EFF'
          }
        }]
      });
    };

    // 保留你原有的生命周期和事件处理代码
    onMounted(() => {
      initChart();
      window.addEventListener('resize', () => {
        chartInstance.value?.resize();
      });
    });

    onUnmounted(() => {
      chartInstance.value?.dispose();
    });

    return {
      chartRef,
      averageScore,
      maxScore,
      minScore,
      handleDownload: () => alert('下载功能'),
      handleWeightSettings: () => alert('设置功能'),
      handleArchive: () => alert('归档功能'),
      Download,
      Setting,
      Box
    };
  }
});
</script>

<style scoped>
.score-container {
  padding: 20px;
  height: 100vh;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-container {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}
</style>    