<template>
  <nav class="nav-container">
    <ul>
      <li 
        v-for="(item, index) in navItems" 
        :key="item.key"
        :class="{ active: activeIndex === index }"
        @click="handleNavClick(index, item.key)"
      >
        {{ item.label }}
      </li>
    </ul>
  </nav>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['nav-click'])
const activeIndex = ref(0)

const navItems = [
  { key: 'digital-media', label: '数媒资源' },
  { key: 'virtual-simulation', label: '虚拟仿真资源' },
  { key: 'open-education', label: '开放教育资源' },
  { key: 'course-management', label: '课程管理' },
  { key: 'cultural-heritage', label: '非遗传承' },
  { key: 'graduation-works', label: '毕业作品' }
]

const handleNavClick = (index, tabKey) => {
  activeIndex.value = index
  emit('nav-click', tabKey)
}
</script>

<style lang="scss" scoped>
@use "sass:color"; // 导入 color 模块

.nav-container {
  position: sticky;
  top: 20px;
  height: fit-content;
  width: 200px;
  background: white;
  border-radius: 4px;
  border: 1px solid #eee;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

  ul {
    list-style: none;
    padding: 0;
    margin: 10px;
  }

  li {
    padding: 8px 12px;
    margin-bottom: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;

     //background: lighten($primary-color, 30%);
    line-height: 1.5;

    &:hover {
      //background: darken(#e9ecef, 5%); 
      //background: darken($hover-bg, 5%); 

     background: lighten(#8a6de3, 30%);
    line-height: 1.5;

    &.active {
      background: #8a6de3;
      color: #fbfbfb;
      font-weight: 600;
      box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>