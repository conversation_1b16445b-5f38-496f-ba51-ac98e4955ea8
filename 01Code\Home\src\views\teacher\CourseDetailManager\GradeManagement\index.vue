<!-- <template>
    <div class="score-management">

        <div class="button-tabs">
            <div class="tab-buttons">
                <button 
                    v-for="tab in tabs" 
                    :key="tab.name"
                    :class="{ 'active': activeTab === tab.name }"
                    @click="activeTab = tab.name"
                >
                    {{ tab.label }}
                </button>
            </div>
            

            <div class="divider"></div>
            

            <div class="content">
                <div v-if="activeTab === 'attendance'">
                    <AttendanceTable/>
                </div>
                <div v-if="activeTab === 'daily'">
                    <ScoreTable/>
                </div>
                <div v-if="activeTab === 'homework'">
                    <p>这里展示作业测试成绩的内容</p>
                </div>
                <div v-if="activeTab === 'exam'">
                    <p>这里展示考试成绩的内容</p>
                </div>
                <div v-if="activeTab === 'total'">
                    <p>这里展示总成绩的内容</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import AttendanceTable from './components/AttendanceTable.vue';
import ScoreTable from './components/ScoreTable.vue';

const activeTab = ref('attendance');

const tabs = [
    { label: '考勤成绩', name: 'attendance' },
    { label: '平时成绩', name: 'daily' },
    { label: '作业测试成绩', name: 'homework' },
    { label: '考试成绩', name: 'exam' },
    { label: '总成绩', name: 'total' }
];
</script>

<style scoped lang="scss">
$gap-size: 15px;
$tab-height: 40px;
$tab-width: 140px;
$border-color: #e4e7ed;
$active-color: #409eff;
$tab-bg-color: #f5f7fa;
$content-bg-color: #fff;
$border-radius: 4px;

.score-management {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    height: 97%;
    display: flex; 
    flex-direction: column; 
}

.button-tabs {
    display: flex;
    flex-direction: column;
    gap: $gap-size;
    flex: 1; 
}

.tab-buttons {
    display: flex;
    margin: 0 auto; 
    border: 1px solid $border-color;
    border-radius: $border-radius;
    overflow: hidden;
    
    button {
        flex: none;
        height: $tab-height;
        min-width: $tab-width;
        background: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 14px;
        color: #606266;
        position: relative;
        
        &:not(:first-child) {
            border-left: 1px solid $border-color;
        }
        
        &:first-child {
            border-top-left-radius: $border-radius;
            border-bottom-left-radius: $border-radius;
        }
        
        &:last-child {
            border-top-right-radius: $border-radius;
            border-bottom-right-radius: $border-radius;
        }
        
        &.active {
            background-color: $active-color;
            color: white;
            border-color: $active-color;
            
            &::before, &::after {
                content: '';
                position: absolute;
                top: 0;
                width: 1px;
                height: 100%;
                background-color: $active-color;
            }
            
            &::before {
                left: -1px;
            }
            
            &::after {
                right: -1px;
            }
        }
        
        &:hover:not(.active) {
            background-color: #f5f7fa;
        }
    }
}

.divider {
    height: 1px;
    background-color: $border-color;
    margin: 10px 0;
    width: 100%;
    flex-shrink: 0; 
}

.content {
    background-color: $content-bg-color;
    border-radius: $border-radius;
    flex: 1; 
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    width: 100%;
    box-sizing: border-box;
    padding: 20px;
    overflow: auto; 
    min-height: 0; 
}
</style> -->

<template>
  <div class="score-management">
    <div class="button-tabs">
      <div class="tab-buttons">
        <button 
          v-for="tab in tabs" 
          :key="tab.name"
          :class="{ 'active': activeTab === tab.name }"
          @click="activeTab = tab.name"
        >
          {{ tab.label }}
        </button>
      </div>
      
      <div class="divider"></div>
      
      <div class="content">
        <!-- 考勤成绩 -->
        <ScrollableTableContainer 
          v-if="activeTab === 'attendance'" 
          title="考勤成绩" 
          :columns="attendanceColumns"
          :data="attendanceData" 
          :classes="classes"
        />
        
        <!-- 平时成绩 -->
        <ScrollableTableContainer 
          v-if="activeTab === 'daily'" 
          title="平时成绩" 
          :columns="dailyScoreColumns"
          :data="dailyScoreData" 
          :classes="classes" 
          cutoffDate="06-29"
        />
        
        <!-- 作业测试成绩 -->
        <ScrollableTableContainer 
          v-if="activeTab === 'homework'" 
          title="作业测试成绩" 
          :columns="homeworkColumns"
          :data="homeworkData" 
          :classes="classes"
        />
        
        <!-- 考试成绩 -->
        <ScrollableTableContainer 
          v-if="activeTab === 'exam'" 
          title="考试成绩" 
          :columns="examColumns" 
          :data="examData"
          :classes="classes"
        />
        
        <div v-if="activeTab === 'total'">
          <TotalScore/>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
import { ref } from 'vue'
import GenericScoreTable from './components/GenericScoreTable.vue'
import TotalScore from './components/TotalScore.vue'
import ScrollableTableContainer from './components/ScrollableTableContainer .vue'


const activeTab = ref('attendance')

// 班级数据
const classes = ref([
    { id: 1, name: '数字媒体技术111班(1)' },
    { id: 2, name: '数字媒体技术111班(2)' },
    { id: 3, name: '数字媒体技术112班(1)' }
])

// 标签页配置
const tabs = [
    { label: '考勤成绩', name: 'attendance' },
    { label: '平时成绩', name: 'daily' },
    { label: '作业测试成绩', name: 'homework' },
    { label: '考试成绩', name: 'exam' },
    { label: '总成绩', name: 'total' }
]

// 考勤成绩列配置
const attendanceColumns = ref([
    { key: 'id', title: '学号', sortable: true },
    { key: 'name', title: '姓名' },
    {
        key: 'attendanceRate',
        title: '出勤率',
        sortable: true,
        formatter: (value) => `${value}%`,
        class: (value) => {
            if (value >= 90) return 'high-rate'
            if (value >= 70) return 'medium-rate'
            return 'low-rate'
        }
    },
    { key: 'attendanceDays', title: '出勤天数', sortable: true },
    { key: 'absenceDays', title: '缺勤天数', sortable: true }
])

// 平时成绩列配置
const dailyScoreColumns = ref([
    { key: 'id', title: '学号', sortable: true },
    { key: 'name', title: '姓名' },
    {
        key: 'progress',
        title: '学习进度分',
        sortable: true,
        formatter: (value) => `${value}%`
    },
    {
        key: 'interaction',
        title: '互动次数',
        sortable: true,
        formatter: (value) => `${value}次`
    },
    {
        key: 'total',
        title: '平时成绩总分',
        sortable: true,
        formatter: (value) => `${value}分`,
        class: (value) => {
            if (value >= 90) return 'high-score'
            if (value >= 60) return 'medium-score'
            return 'low-score'
        }
    }
])

// 作业测试成绩列配置
const homeworkColumns = ref([
    { key: 'id', title: '学号', sortable: true },
    { key: 'name', title: '姓名' },
    { key: 'homeworkCount', title: '作业次数', sortable: true },
    { key: 'completedCount', title: '完成次数', sortable: true },
    { key: 'averageScore', title: '平均分', sortable: true },
    { key: 'totalScore', title: '作业总分', sortable: true },
    { key: 'totalScore', title: '作业总分', sortable: true },
    { key: 'totalScore', title: '作业总分', sortable: true },
    { key: 'totalScore', title: '作业总分', sortable: true },
    { key: 'totalScore', title: '作业总分', sortable: true }
])

// 考试成绩列配置
const examColumns = ref([
    { key: 'id', title: '学号', sortable: true },
    { key: 'name', title: '姓名' },
    { key: 'midterm', title: '期中成绩', sortable: true },
    { key: 'final', title: '期末成绩', sortable: true },
    { key: 'average', title: '平均成绩', sortable: true }
])

// 总成绩列配置
const totalScoreColumns = ref([
    { key: 'id', title: '学号', sortable: true },
    { key: 'name', title: '姓名' },
    { key: 'attendanceScore', title: '考勤成绩', sortable: true },
    { key: 'dailyScore', title: '平时成绩', sortable: true },
    { key: 'homeworkScore', title: '作业成绩', sortable: true },
    { key: 'examScore', title: '考试成绩', sortable: true },
    { key: 'examScore', title: '考试成绩', sortable: true },
    { key: 'examScore', title: '考试成绩', sortable: true },
    { key: 'examScore', title: '考试成绩', sortable: true },
    { key: 'examScore', title: '考试成绩', sortable: true },
    { key: 'examScore', title: '考试成绩', sortable: true },
    { key: 'examScore', title: '考试成绩', sortable: true },
    {
        key: 'totalScore',
        title: '总成绩',
        sortable: true,
        class: (value) => {
            if (value >= 90) return 'high-total'
            if (value >= 60) return 'medium-total'
            return 'low-total'
        }
    }
])

// 考勤测试数据
const attendanceData = ref([
    {
        id: '2300470107',
        name: '潘颖珊',
        classId: 1,
        attendanceRate: 100,
        attendanceDays: 20,
        absenceDays: 0
    },
    {
        id: '2300470108',
        name: '张三',
        classId: 1,
        attendanceRate: 85,
        attendanceDays: 17,
        absenceDays: 3
    },
    {
        id: '2300470109',
        name: '李四',
        classId: 2,
        attendanceRate: 70,
        attendanceDays: 14,
        absenceDays: 6
    },
    {
        id: '2300470110',
        name: '王五',
        classId: 3,
        attendanceRate: 95,
        attendanceDays: 19,
        absenceDays: 1
    }
])

// 平时成绩测试数据
const dailyScoreData = ref([
    {
        id: '2300470107',
        name: '潘颖珊',
        classId: 1,
        progress: 100,
        interaction: 15,
        total: 95
    },
    {
        id: '2300470108',
        name: '张三',
        classId: 1,
        progress: 80,
        interaction: 8,
        total: 78
    },
    {
        id: '2300470109',
        name: '李四',
        classId: 2,
        progress: 65,
        interaction: 5,
        total: 62
    },
    {
        id: '2300470110',
        name: '王五',
        classId: 3,
        progress: 92,
        interaction: 12,
        total: 88
    }
])

// 作业测试成绩数据
const homeworkData = ref([
    {
        id: '2300470107',
        name: '潘颖珊',
        classId: 1,
        homeworkCount: 10,
        completedCount: 10,
        averageScore: 95,
        totalScore: 95
    },
    {
        id: '2300470108',
        name: '张三',
        classId: 1,
        homeworkCount: 10,
        completedCount: 8,
        averageScore: 78,
        totalScore: 78
    },
    {
        id: '2300470109',
        name: '李四',
        classId: 2,
        homeworkCount: 10,
        completedCount: 7,
        averageScore: 65,
        totalScore: 65
    },
    {
        id: '2300470110',
        name: '王五',
        classId: 3,
        homeworkCount: 10,
        completedCount: 9,
        averageScore: 88,
        totalScore: 88
    }
])

// 考试成绩数据
const examData = ref([
    {
        id: '2300470107',
        name: '潘颖珊',
        classId: 1,
        midterm: 92,
        final: 95,
        average: 93.5
    },
    {
        id: '2300470108',
        name: '张三',
        classId: 1,
        midterm: 78,
        final: 82,
        average: 80
    },
    {
        id: '2300470109',
        name: '李四',
        classId: 2,
        midterm: 65,
        final: 70,
        average: 67.5
    },
    {
        id: '2300470110',
        name: '王五',
        classId: 3,
        midterm: 85,
        final: 90,
        average: 87.5
    }
])

// 总成绩数据
const totalScoreData = ref([
    {
        id: '2300470107',
        name: '潘颖珊',
        classId: 1,
        attendanceScore: 100,
        dailyScore: 95,
        homeworkScore: 95,
        examScore: 93.5,
        totalScore: 95.8
    },
    {
        id: '2300470108',
        name: '张三',
        classId: 1,
        attendanceScore: 85,
        dailyScore: 78,
        homeworkScore: 78,
        examScore: 80,
        totalScore: 80.2
    },
    {
        id: '2300470109',
        name: '李四',
        classId: 2,
        attendanceScore: 70,
        dailyScore: 62,
        homeworkScore: 65,
        examScore: 67.5,
        totalScore: 66.1
    },
    {
        id: '2300470110',
        name: '王五',
        classId: 3,
        attendanceScore: 95,
        dailyScore: 88,
        homeworkScore: 88,
        examScore: 87.5,
        totalScore: 89.5
    }
])
</script>

<style scoped lang="scss">
/* 保持原有的样式不变 */
.score-management {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    height: 97%;
    display: flex;
    flex-direction: column;
}

.button-tabs {
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex: 1;
}

.tab-buttons {
    display: flex;
    margin: 0 auto;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;

    button {
        flex: none;
        height: 40px;
        min-width: 140px;
        background: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 14px;
        color: #606266;
        position: relative;

        &:not(:first-child) {
            border-left: 1px solid #e4e7ed;
        }

        &:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }

        &:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        &.active {
            background-color: #409eff;
            color: white;
            border-color: #409eff;

            &::before,
            &::after {
                content: '';
                position: absolute;
                top: 0;
                width: 1px;
                height: 100%;
                background-color: #409eff;
            }

            &::before {
                left: -1px;
            }

            &::after {
                right: -1px;
            }
        }

        &:hover:not(.active) {
            background-color: #f5f7fa;
        }
    }
}

.divider {
    height: 1px;
    background-color: #e4e7ed;
    margin: 10px 0;
    width: 100%;
    flex-shrink: 0;
}

.content {
    background-color: #fff;
    border-radius: 4px;
    flex: 1;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    width: 100%;
    box-sizing: border-box;
    padding: 20px;
    overflow: auto;
    min-height: 0;
}
</style>