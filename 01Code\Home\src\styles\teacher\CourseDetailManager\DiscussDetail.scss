//src\styles\teacher\CourseDetailManager\DiscussDetail.scss
@use "@/styles/variables.scss" as *;

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  color: #333; 
}

img {
  max-width: 100%;
  height: auto;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  outline: none;
  font-family: inherit;
}

textarea {
  resize: none;
  font-family: inherit;
}

// 导航栏样式
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 54px;
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  z-index: 100;

  .nav-bar-left {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    padding-left: 16px;

    .back-btn {
      display: flex;
      align-items: center;
      color: #333;
      font-size: 14px;

      .iconfont {
        margin-right: 4px;
        display: flex;
        align-items: center;

        img {
          width: 20px;
          height: 20px;
        }
      }
    }
  }

  .nav-bar-center {
    margin: 0 auto;
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
}

// 主体内容容器
.discuss-detail {
  max-width: 90vw;
  min-width: 480px;
  padding: 64px 16px 20px;
  margin: 20px auto;

  // 话题信息模块
  .topic-info {
    background-color: #fff;
    border-radius: 8px;
    padding: 32px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;

    .topic-title {
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 12px;
      line-height: 1.4;

      .pinned-tag {
        color: #ff4d4f;
        margin-right: 6px;
        font-weight: bold;
        font-size: 1.3rem;
      }
    }

    .topic-content {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 16px;
      color: #333;
    }

    .topic-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;

      .stats {
        font-size: 12px;
        color: #999;
        display: flex;
        gap: 16px;
      }

      .operation {
        display: flex;
        align-items: center;
        gap: 10px;

        .reply-btn {
          padding: 6px 12px;
          border-radius: 4px;
          background-color: $primary-color; 
          color: #fff;
          font-size: 14px;
          display: flex;
          align-items: center;

          .iconfont {
            margin-right: 4px;
          }

          &:hover {
            background-color: $button-hover; 
          }
        }

        .more-actions {
          position: relative;

          .more-btn {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            color: #999;

            &:hover {
              background-color: #f0f0f0;
            }
          }

          .action-menu {
            position: absolute;
            right: 0;
            top: 100%;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 8px 0;
            min-width: 120px;
            display: none;
            z-index: 10;

            button {
              width: 100%;
              padding: 8px 16px;
              text-align: left;
              font-size: 14px;
              color: #333;
              display: flex;
              align-items: center;

              .iconfont {
                margin-right: 8px;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                  width: 16px; 
                  height: 16px; 
                  object-fit: contain; 
                }
              }

              &:hover {
                background-color: #f5f5f5;
              }
            }
          }

          &:hover .action-menu {
            display: block;
          }
          
        }
      }
    }

    .publisher-info {
      display: flex;
      align-items: center;
      margin-top: 20px;

      .avatar-container {
        position: relative;
        width: 24px;
        height: 24px; 
        flex-shrink: 0; 
        margin-right: 12px;

        .avatar {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          object-fit: cover; 
          background-color: #f0f2f5;
          display: block; 
        }

        .teacher-badge {
          position: absolute;
          right: -3px;
          bottom: -3px;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background-color: $primary-color; 
          color: white;
          font-size: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 1;
          border: 2px solid white;
        }
      }

      .publisher-meta {
        margin-left: 8px;
        font-size: 12px;

        .publisher-name {
          color: #333;
          margin-right: 8px;
        }

        .publisher-school {
          color: #999;
        }
      }
    }
  }

  // 回答输入面板
  .answer-panel {
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .answer-textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      font-size: 14px;
      color: #333;
      margin-bottom: 12px;

      &:focus {
        outline: none;
        border-color: $primary-color; 
      }
    }

    .answer-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .tip {
        font-size: 12px;
        color: #999;
      }

      .btn-group {
        display: flex;
        gap: 8px;

        .cancel-btn {
          padding: 6px 12px;
          border-radius: 4px;
          border: 1px solid #e5e5e5;
          color: #333;
          font-size: 14px;

          &:hover {
            background-color: #f5f5f5;
          }
        }

        .submit-btn {
          padding: 6px 12px;
          border-radius: 4px;
          background-color: $primary-color; 
          color: #fff;
          font-size: 14px;

          &:hover {
            background-color: $button-hover; 
          }
        }
      }
    }
  }
}

.star-bth {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  color: #333;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  .iconfont {
    margin-right: 8px;
  }
}

.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  
  .edit-modal-content {
    background-color: #fff;
    border-radius: 8px;
    width: 600px;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    
    .edit-modal-header {
      padding: 16px 24px;
      border-bottom: 1px solid #e5e5e5;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        font-size: 18px;
        color: #333;
        margin: 0;
      }
      
      .close-btn {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        color: #999;
        background: none;
        border: none;
        
        &:hover {
          background-color: #f5f5f5;
        }
        
        .iconfont img {
          width: 16px;
          height: 16px;
        }
      }
    }
    
    .edit-modal-body {
      padding: 24px;
      flex: 1;
      overflow-y: auto;
      
      .edit-title {
        width: 100%;
        padding: 12px;
        border: 1px solid #e5e5e5;
        border-radius: 4px;
        font-size: 16px;
        margin-bottom: 16px;
        
        &:focus {
          outline: none;
          border-color: $primary-color;
        }
      }
      
      .edit-content {
        width: 100%;
        padding: 12px;
        border: 1px solid #e5e5e5;
        border-radius: 4px;
        font-size: 14px;
        min-height: 200px;
        
        &:focus {
          outline: none;
          border-color: $primary-color;
        }
      }
    }
    
    .edit-modal-footer {
      padding: 16px 24px;
      border-top: 1px solid #e5e5e5;
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      
      .cancel-btn {
        padding: 8px 16px;
        border-radius: 4px;
        border: 1px solid #e5e5e5;
        color: #333;
        font-size: 14px;
        background: none;
        
        &:hover {
          background-color: #f5f5f5;
        }
      }
      
      .submit-btn {
        padding: 8px 16px;
        border-radius: 4px;
        background-color: $primary-color;
        color: #fff;
        font-size: 14px;
        border: none;
        
        &:hover {
          background-color: $button-hover;
        }
        
        &:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }
      }
    }
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .edit-modal-content {
    width: 95% !important;
    
    .edit-modal-body {
      padding: 16px !important;
    }
  }
}