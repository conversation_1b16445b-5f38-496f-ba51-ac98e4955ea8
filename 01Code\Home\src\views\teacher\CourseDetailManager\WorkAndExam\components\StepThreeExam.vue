<template>
    <div style="margin-bottom: 100px;">
        <h3 class="sub-title">基础设置</h3>
        <el-form label-position="top" ref="formRef">
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="满分值（由题目自动计算）">
                        <el-input v-model="form.fullScore" disabled />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="考试时长（分钟）">
                        <el-input-number v-model="form.examDuration" :min="1" style="width: 100%;"
                            @change="updateExamEndTime" />
                    </el-form-item>
                </el-col>
            </el-row>

            <h3 class="sub-title">时间安排</h3>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="发布时间">
                        <el-date-picker v-model="form.publishTime" type="datetime" placeholder="请选择发布时间" value-format="x"
                            style="width: 300px;" />
                    </el-form-item>
                </el-col>

                <el-col :span="8">
                    <el-form-item label="考试时间">
                        <div class="datetime-range">
                            <el-date-picker v-model="form.examStart" type="datetime" placeholder="开始时间" style="width: 45%;"
                                value-format="x" @change="updateExamEndTime" />
                            <span style="margin: 0 8px;">至</span>
                            <el-date-picker v-model="form.examEnd" type="datetime" placeholder="结束时间" style="width: 45%;"
                                value-format="x" disabled />
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>

            <h4 class="sub-title">防作弊设置</h4>
            <el-checkbox-group v-model="form.antiCheatOptions">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-checkbox label="限制答案粘贴" />
                    </el-col>
                    <el-col :span="8">
                        <el-checkbox label="限制多台设备同时做答" />
                    </el-col>
                    <el-col :span="8">
                        <el-checkbox label="限制考试切屏" />
                    </el-col>
                    <el-col :span="8">
                        <el-checkbox label="限制提前交卷" />
                    </el-col>
                </el-row>
            </el-checkbox-group>

        </el-form>
    </div>
</template>
  
<script setup>
import { ref } from "vue";

const props = defineProps({
    form: Object,
});

const formRef = ref(null);

function updateExamEndTime() {
    if (!props.form.examStart || !props.form.examDuration) return;

    const startTimestamp = Number(props.form.examStart);
    const durationMs = Number(props.form.examDuration) * 60 * 1000;
    props.form.examEnd = startTimestamp + durationMs;
}
</script>
  
<style scoped>
.datetime-range {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}
</style>
  