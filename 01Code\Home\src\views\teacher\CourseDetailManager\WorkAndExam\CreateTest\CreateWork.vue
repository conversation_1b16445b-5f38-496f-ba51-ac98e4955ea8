<template>
    <div class="nav-container">
        <div class="nav-bar">
            <el-button class="back-button" type="info" plain @click="handleBack">
                <el-icon>
                    <ArrowLeft />
                </el-icon> 返回
            </el-button>

            <div class="steps-container">
                <el-steps :active="active" finish-status="success" align-center>
                    <el-step title="基本信息" />
                    <el-step title="选题组卷" />
                    <el-step :title="mode === 'exam' ? '考试设置' : '作业设置'" />
                    <el-step title="发布班级（选填）" />
                </el-steps>
            </div>

            <div class="action-buttons">
                <el-button type="default" @click="handleSave">保存</el-button>
                <el-button type="primary" @click="handlePreviewBtn">预览</el-button>
            </div>
        </div>

        <div class="main_content">
            <!-- 第一步：基本信息 -->
            <div v-if="active === 0">
                <h2>基本信息</h2>
                <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" label-position="top">
                    <el-form-item :label="mode === 'exam' ? '考试标题' : '作业标题'" prop="title">
                        <el-input v-model="form.title" maxlength="50" show-word-limit placeholder="请输入标题" />
                    </el-form-item>

                    <el-form-item label="描述">
                        <div class="quill-wrapper">
                            <QuillEditor v-model="form.description" />
                        </div>
                    </el-form-item>

                    <el-form-item label="上传附件">
                        <el-upload :file-list="fileList" :on-change="handleChange" :on-remove="handleRemove"
                            :on-preview="handlePreview" :auto-upload="false">
                            <el-button type="primary">+ 上传文件</el-button>
                        </el-upload>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 第二步：选题组卷 -->
            <div v-if="active === 1">
                <h2>选题组卷（选填）</h2>

                <el-table v-if="questionPapers.length" :data="questionPapers" style="width: 100%" border>
                    <el-table-column prop="name" label="题目标题" />
                    <el-table-column label="题型" width="100">
                        <template #default="{ row }">
                            {{ getTypeName(row.questionType) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="questionCount" label="题目数量" width="120" />
                    <el-table-column label="分值" width="160">
                        <template #default="{ row }">
                            <el-input-number v-model="row.score" :min="1" :max="100" :step="1" size="small"
                                controls-position="right" placeholder="分值" />
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80">
                        <template #default="{ $index }">
                            <el-button style="border: 0px;" size="small" @click="removeQuestion($index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <el-empty v-else description="暂无题库，请点击下方按钮添加" />

                <div style="text-align: center; margin: 26px 0;">
                    <el-button type="primary" @click="goToQuestionBank">+ 添加题库</el-button>
                </div>
            </div>

            <!-- 第三步：动态渲染 -->
            <div v-else-if="active === 2">
                <StepThreeAssignment v-if="mode === 'assignment'" v-model:form="form" :all-classes="allClasses" />
                <StepThreeExam v-if="mode === 'exam'" :form="form" :all-classes="allClasses" />
            </div>

            <!-- 第四步：发布班级 -->
            <div v-else-if="active === 3">
                <h2>发布班级（选填）</h2>
                <el-select v-model="selectedPublishClasses" multiple placeholder="请选择班级" style="width: 30%">
                    <el-option v-for="cls in allClasses" :key="cls.id" :label="cls.name" :value="cls.id" />
                </el-select>
                <div style="text-align: center; margin-top: 24px">
                    <el-button @click="handleBack">返回</el-button>
                    <el-button type="primary" @click="confirmPublish">完成发布</el-button>
                </div>
            </div>

            <!-- 步骤按钮只在第0~2步显示 -->
            <div class="step-control" v-if="active < 3">
                <el-button v-if="active > 0" @click="prevStep">上一步</el-button>
                <el-button type="primary" @click="active < 2 ? nextStep() : handleFinish()">
                    {{ active < 2 ? "下一步" : "完成" }} </el-button>
            </div>

        </div>

        <!-- 预览弹窗 -->
        <el-dialog v-model="showPreviewDialog" title="试卷预览" width="800px">
            <ExamPreviewDialog :examData="previewExamData" />
        </el-dialog>
    </div>
</template>

<script>
import { ref, reactive, onMounted, watchEffect, onActivated } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowLeft } from "@element-plus/icons-vue";

import StepThreeAssignment from "../components/StepThreeAssignment.vue";
import StepThreeExam from "../components/StepThreeExam.vue";
import QuillEditor from "../components/QuillEditor.vue";
import ExamPreviewDialog from "../components/ExamPreviewDialog.vue";

import { useQuestionStore } from '@/stores/useQuestionStore'

const testStore = useQuestionStore()

//存储设置
import { useCreateWorkStore } from '@/stores/useCreateWorkStore'
const createWorkStore = useCreateWorkStore()

//存储设置
import { useAssignmentStore } from '@/stores/useAssignmentStore'
const assignmentStore = useAssignmentStore();

import {
    addAssignmentOrExam,
    updateAssignmentOrExam,
    bindToCourse,
    bindToClass,
    batchSaveAssignmentRelation,
    batchSaveExamRelation,
    fetchAssignmentOrExamDetail
} from "@/api/teacher/assignment";

import { fetchClassList } from "@/api/teacher/class";

// 题目详情获取
import {
    getSingleChoiceDetail,
    getMultipleChoiceDetail,
    getTrueFalseDetail,
    getFillBlankDetail,
    getShortAnswerDetail,
    getExamDetail,
    getPracticeProjectDetail
} from '@/api/teacher/question'

// 根据题型调用对应详情接口
async function fetchQuestionDetail(questionType, questionId) {
    switch (questionType) {
        case 0: return getSingleChoiceDetail({ id: questionId })
        case 1: return getMultipleChoiceDetail({ id: questionId })
        case 2: return getTrueFalseDetail({ id: questionId })
        case 3: return getFillBlankDetail({ id: questionId })
        case 4: return getShortAnswerDetail({ id: questionId })
        default: throw new Error('未知题型')
    }
}

async function saveSelectedQuestions(assignmentId, isExam, selectedQuestions) {
    try {
        const detailedQuestions = await Promise.all(
            selectedQuestions.map(async q => {
                const res = await fetchQuestionDetail(q.questionType, q.questionId)
                return {
                    ...res.result,
                    questionType: q.questionType,
                    questionId: q.questionId,
                    score: q.score || 5, // 默认分数可调整
                }
            })
        )

        const relations = detailedQuestions.map(q => ({
            id: '',
            assignmentId: isExam ? '' : assignmentId,
            examId: isExam ? assignmentId : '',
            questionType: q.questionType,
            questionId: q.questionId,
            score: q.score
        }))

        if (isExam) {
            await batchSaveExamRelation({ examId: assignmentId, relations })
        } else {
            await batchSaveAssignmentRelation({ assignmentId, relations })
        }

        ElMessage.success('题目已成功关联')
    } catch (err) {
        console.error('保存题目关联失败:', err)
        ElMessage.error('保存失败：' + (err.message || '未知错误'))
    }
}

export default {
    components: {
        ExamPreviewDialog,
        ArrowLeft,
        StepThreeAssignment,
        StepThreeExam,
        QuillEditor,
    },
    setup() {
        const router = useRouter();
        const route = useRoute();
        const active = ref(0);
        const mode = ref(route.query.mode || "assignment"); // assignment 或 exam
        const isEditMode = ref(false);
        const originId = ref("");

        const courseId = route.params.courseId
        const courseName = route.params.courseName

        console.log('课程ID:', courseId)
        console.log('课程名:', courseName)

        const submitting = ref(false); // 防止重复提交

        const formRef = ref(null);
        const selectedTests = ref([]);
        const hasSavedRelations = ref(false);


        const form = reactive({
            title: "",
            description: "",
            isPublic: false,
            paper: { name: "", questionCount: 1 },
            selectedClasses: [],
            endTime: null,
            deadline: null,
            answerSettings: [],
            fullScore: 0,
            examDuration: 90,
            examStart: null,
            examEnd: null,
            publishTime: null,
        });


        const fileList = ref([]);
        const questionPapers = ref([]);
        const allClasses = ref([]);

        const rules = {
            title: [
                { required: true, message: "请输入标题", trigger: "blur" },
                { max: 50, message: "标题最多50个字", trigger: "blur" },
            ],
        };

        // 自动计算总分（watchEffect）
        watchEffect(() => {
            form.fullScore = questionPapers.value.reduce((sum, p) => {
                // 分数校验，负数置为0
                if (typeof p.score !== 'number' || p.score < 0) p.score = 0;
                return sum + (p.score || 0);
            }, 0);
        });

        // 编辑模式下拉取详情，填充表单和题目
        async function loadDetail(id) {
            try {
                const res = await fetchAssignmentOrExamDetail(id, mode.value === 'exam');
                if (res.code === 200 && res.result) {
                    const data = res.result;
                    form.title = data.title || "";
                    form.description = data.description || "";
                    form.isPublic = data.isPublic || false;
                    form.publishTime = data.publishTime || null;

                    if (mode.value === "assignment") {
                        form.deadline = data.endTime || null;
                    } else {
                        form.examStart = data.startTime || null;
                        form.examEnd = data.endTime || null;
                        form.examDuration = data.durationMin || 90;
                    }

                    // 加载题目列表（根据返回结构改写）
                    questionPapers.value = data.questionList?.map((q) => ({
                        name: q.name || "未知题目",
                        questionCount: q.questionCount || 1,
                        questionId: q.questionId,
                        questionType: q.questionType,
                        score: q.score || 10,
                    })) || [];
                }
            } catch (err) {
                ElMessage.error({ message: "加载详情失败", offset: 90 });
                console.error(err);
            }
        }

        function appendSelectedTests(clearAfterAppend = true) {
            if (testStore.selectedTests.length > 0) {
                console.log('Pinia中获取到 selectedTests：', testStore.selectedTests)
                const newPapers = testStore.selectedTests.map((t) => ({
                    name: t.title || "未知题目",
                    questionCount: 1,
                    questionId: t.id,
                    questionType: mapTypeToCode(t.type),
                    score: 10,
                }));

                const existingIds = new Set(questionPapers.value.map((p) => p.questionId));
                newPapers.forEach((p) => {
                    if (!existingIds.has(p.questionId)) {
                        questionPapers.value.push(p);
                    }
                });

                if (clearAfterAppend) {
                    testStore.clearTests();
                }
            }
        }

        onMounted(async () => {

            const isFromQuestionBank = route.query.step === '1'

            if (!isFromQuestionBank) {
                // 如果不是是从题库返回，则是新建一次新的作业/考试
                testStore.clearTests();                  // 清空 Pinia 题库临时选题
                questionPapers.value = [];               // 清空已选题列表
                createWorkStore.clearStepOne();       // 清空第一步缓存

                Object.assign(form, {
                    title: "",
                    description: "",
                    isPublic: false,
                    paper: { name: "", questionCount: 1 },
                    selectedClasses: [],
                    endTime: null,
                    deadline: null,
                    answerSettings: [],
                    fullScore: 0,
                    examDuration: 90,
                    examStart: null,
                    examEnd: null,
                    publishTime: null,
                });
            } else {
                // 如果是从题库返回，恢复保存的第一步数据
                Object.assign(form, createWorkStore.formStepOne);
            }

            appendSelectedTests(false);

            // 获取班级列表
            try {
                const res = await fetchClassList({ courseId: route.query.courseId });
                if (res.code === 200 && Array.isArray(res.result)) {
                    allClasses.value = res.result;
                    console.log("班级列表:", res.result);
                } else {
                    ElMessage.warning({ message: "无法获取班级列表", offset: 90 });
                }
            } catch (err) {
                ElMessage.error({ message: "获取班级列表失败", offset: 90 });
            }

            // 处理步骤跳转
            if (route.query.step) {
                const stepNum = Number(route.query.step);
                if (!isNaN(stepNum) && stepNum >= 0 && stepNum <= 3) {
                    active.value = stepNum;
                }
            }

            // 编辑模式加载详情
            if (route.query.edit === "true" && route.query.id) {
                isEditMode.value = true;
                originId.value = route.query.id;
                await loadDetail(originId.value);
            }
        });


        onActivated(() => {
            appendSelectedTests(true);
        })

        // 防止重复提交按钮禁用
        const isSubmitting = ref(false);

        function mapTypeToCode(label) {
            const map = {
                "单选题": 0,
                "多选题": 1,
                "判断题": 2,
                "填空题": 3,
                "问答题": 4
            };
            return map[label] ?? 0;
        }

        // 防止重复提交
        async function submitForm(isDraft = false) {
            if (submitting.value) return false;
            submitting.value = true;
            try {
                // 题目分数校验
                if (questionPapers.value.some(p => p.score <= 0)) {
                    ElMessage.error({ message: "题目分数必须大于0", offset: 90 });
                    return false;
                }

                const payload = {
                    title: form.title,
                    status: isDraft ? 0 : 1,
                    totalScore: form.fullScore || 0,
                    isPublic: form.isPublic,
                    courseId: route.params.courseId,

                    ...(mode.value === "assignment"
                        ? {
                            endTime: form.deadline ? Number(form.deadline) : null,
                        }
                        : {
                            examType: 0,
                            durationMin: form.examDuration || 0,
                            startTime: form.examStart ? Number(form.examStart) : null,
                            endTime: form.examEnd ? Number(form.examEnd) : null,
                        }),

                    publishTime: form.publishTime ? Number(form.publishTime) : null,
                };

                let res;
                if (isEditMode.value) {
                    res = await updateAssignmentOrExam(
                        { id: originId.value, ...payload },
                        mode.value === "exam"
                    );
                    ElMessage.success({
                        message: isDraft ? "草稿保存成功" : "修改成功",
                        offset: 90,
                    });
                    setTimeout(handleBack, 1000);
                } else {
                    res = await addAssignmentOrExam(payload, mode.value === "exam");
                    createdId.value = res.result?.id;

                    // 绑定题目关系
                    if (createdId.value && questionPapers.value.length > 0 && !hasSavedRelations.value) {
                        try {
                            const simplifiedQuestions = questionPapers.value.map(p => ({
                                questionId: p.questionId,
                                questionType: p.questionType,
                                score: p.score || 10,
                            }));
                            await saveSelectedQuestions(createdId.value, mode.value === 'exam', simplifiedQuestions);
                            hasSavedRelations.value = true;
                        } catch (err) {
                            ElMessage.error({ message: '题目绑定失败，请稍后手动添加', offset: 90 });
                        }
                    }


                    ElMessage.success({
                        message: isDraft ? "草稿保存成功" : "创建成功",
                        offset: 90,
                    });

                    if (isDraft) {
                        setTimeout(handleBack, 1000);
                    } else {
                        active.value = 3;
                    }
                }
                return true;
            } catch (err) {
                ElMessage.error({ message: isDraft ? "保存失败" : "创建失败", offset: 90 });
                console.error(err);
                return false;
            } finally {
                submitting.value = false;
            }
        }

        // 时间范围校验工具
        function isValidTimeRange(start, end) {
            return start && end && Number(end) > Number(start);
        }

        // 完成提交，带时间校验
        async function handleFinish() {
            const now = Date.now();

            if (mode.value === "assignment") {
                if (!form.deadline) {
                    ElMessage.warning({ message: "请填写截止时间", offset: 90 });
                    return;
                }
                if (Number(form.deadline) <= now) {
                    ElMessage.warning({ message: "截止时间不能早于当前时间", offset: 90 });
                    return;
                }
                if (form.publishTime && Number(form.deadline) <= Number(form.publishTime)) {
                    ElMessage.error({ message: "作业截止时间必须晚于发布时间！", offset: 90 });
                    return false;
                }
                if (form.publishTime && Number(form.publishTime) <= now) {
                    ElMessage.warning({ message: "发布时间不能早于当前时间", offset: 90 });
                    return;
                }
            } else if (mode.value === "exam") {
                if (!form.publishTime || !form.examStart || !form.examEnd) {
                    ElMessage.warning({ message: "请填写考试时间", offset: 90 });
                    return;
                }
                if (!isValidTimeRange(form.examStart, form.examEnd)) {
                    ElMessage.error({ message: "考试截止时间必须晚于开始时间！", offset: 90 });
                    return false;
                }
                if (!isValidTimeRange(form.publishTime, form.examStart)) {
                    ElMessage.error({ message: "考试发布时间必须早于考试开始时间！", offset: 90 });
                    return false;
                }
                if (Number(form.publishTime) <= now) {
                    ElMessage.warning({ message: "发布时间不能早于当前时间", offset: 90 });
                    return;
                }
            }


            // 题目分数非空校验
            if (questionPapers.value.length === 0) {
                ElMessage.warning({ message: "请至少添加一道题目", offset: 90 });
                return;
            }

            const success = await submitForm(false);
            if (success && !isEditMode.value) {
                // 绑定课程
                try {
                    await bindToCourse(createdId.value, route.params.courseId, mode.value === "exam");
                } catch (err) {
                    ElMessage.error({ message: "绑定课程失败，请稍后重试", offset: 90 });
                    return;
                }

                // 跳转第四步发布班级
                active.value = 3;
            }
        }

        // 保存草稿弹窗与调用
        function handleSave() {
            if (active.value < 1 || !questionPapers.value.length) {
                ElMessage.warning({ message: "请至少在第二步添加题目后再保存！", offset: 90 });
                return;
            }
            ElMessageBox.confirm(
                "保存为草稿后，仅支持修改标题、状态、截止时间和是否公开。确定要保存为草稿吗？",
                "保存提示",
                {
                    confirmButtonText: "确定保存",
                    cancelButtonText: "取消",
                    type: "warning"
                }
            )
                .then(() => {
                    submitForm(true);
                })
                .catch(() => {
                    ElMessage.info({ message: "已取消保存", offset: 90 });
                });
        }

        // 预览弹窗相关
        const showPreviewDialog = ref(false);
        const previewExamData = ref({
            title: '',
            description: '',
            totalScore: 0,
            examStart: null,
            examEnd: null,
            deadline: null,
            examDuration: null,
            questions: [],
        });

        async function handlePreviewBtn() {
            if (!form.title) {
                ElMessage.warning({ message: "请先填写标题再进行预览！", offset: 90 });
                return;
            }

            try {
                // 根据questionPapers中每个题目的id和类型请求详细信息
                const detailedQuestions = await Promise.all(
                    questionPapers.value.map(async (q) => {
                        const res = await fetchQuestionDetail(q.questionType, q.questionId);
                        if (res.code === 200 && res.result) {
                            return {
                                ...res.result,
                                questionType: q.questionType,
                                questionId: q.questionId,
                                score: q.score || 10,
                            };
                        } else {
                            return {
                                content: "题目信息加载失败",
                                questionType: q.questionType,
                                questionId: q.questionId,
                                score: q.score || 10,
                            };
                        }
                    })
                );

                // 组装传给预览弹窗的数据
                previewExamData.value = {
                    title: form.title,
                    description: form.description,
                    totalScore: form.fullScore,
                    examStart: form.examStart,
                    examEnd: form.examEnd,
                    deadline: form.deadline,
                    examDuration: form.examDuration,
                    questions: detailedQuestions,
                };

                showPreviewDialog.value = true;
            } catch (error) {
                ElMessage.error({ message: "加载题目详情失败", offset: 90 });
                console.error(error);
            }
        }

        // 绑定到班级发布
        const createdId = ref("");
        const selectedPublishClasses = ref([]);

        async function confirmPublish() {
            if (!createdId.value) {
                handleBack();
                return;
            }

            if (selectedPublishClasses.value.length === 0) {
                ElMessage.info({ message: "未选择班级，已跳过发布", offset: 90 });
                handleBack();
                return;
            }

            try {
                for (const classId of selectedPublishClasses.value) {
                    await bindToClass({
                        id: createdId.value,
                        courseId: route.params.courseId,  // 传递课程ID
                        classId,
                        isExam: mode.value === "exam"
                    });
                }
                ElMessage.success({ message: "发布成功！", offset: 90 });
                handleBack();
            } catch (err) {
                ElMessage.error({ message: "发布失败，请稍后重试", offset: 90 });
            }
        }

        // 其他辅助
        function handleBack() {
            router.push({
                name: "AssignmentExamHome",
                params: { courseId: route.params.courseId },
                query: { reload: true, t: Date.now() },
            });
        }
        function goToQuestionBank() {
            assignmentStore.saveDraft({ ...form }, questionPapers.value, active.value);
            router.push({
                name: 'AssignmentExamPublicDatabase',
                query: {
                    from: mode.value === 'exam' ? 'create-exam' : 'create-work',
                    courseId,
                    courseName,
                    step: 1,
                    mode: mode.value
                }
            });
        }

        function nextStep() {
            if (active.value === 0) {
                formRef.value.validate((valid) => {
                    if (valid) {
                        // 保存第一步数据到 Store
                        createWorkStore.saveStepOne({
                            title: form.title,
                            description: form.description,
                            isPublic: form.isPublic,
                            deadline: form.deadline,
                            examStart: form.examStart,
                            examEnd: form.examEnd,
                            examDuration: form.examDuration,
                            publishTime: form.publishTime,
                        });
                        active.value++;
                    } else {
                        ElMessage.warning({ message: "请填写完整内容", offset: 90 });
                    }
                });
            } else {
                active.value++;
            }
        }

        function prevStep() {
            active.value--;
        }

        function handleStepBack() {
            if (active.value === 3) {
                handleBack();
            } else {
                prevStep();
            }
        }

        function getTypeName(code) {
            const map = {
                0: "单选题",
                1: "多选题",
                2: "判断题",
                3: "填空题",
                4: "问答题"
            };
            return map[code] || "未知题型";
        }

        function removeQuestion(index) {
            questionPapers.value.splice(index, 1);
        }

        function handleChange(file, fileListNew) {
            fileList.value = fileListNew;
        }
        function handleRemove(file, fileListNew) {
            fileList.value = fileListNew;
        }
        function handlePreview(file) {
            window.open(file.url || "", "_blank");
        }

        return {
            active,
            mode,
            formRef,
            form,
            fileList,
            questionPapers,
            allClasses,
            rules,
            handleChange,
            handleRemove,
            handlePreview,
            handlePreviewBtn,
            handleBack,
            goToQuestionBank,
            nextStep,
            prevStep,
            handleStepBack,
            handleSave,
            handleFinish,
            selectedPublishClasses,
            confirmPublish,
            isEditMode,
            showPreviewDialog,
            getTypeName,
            removeQuestion,
            submitting,
            previewExamData,
            showPreviewDialog,
            createdId,
        };
    }
}

</script>

<style scoped>
@import "@/styles/teacher/CreateCommon.scss";
</style>
  