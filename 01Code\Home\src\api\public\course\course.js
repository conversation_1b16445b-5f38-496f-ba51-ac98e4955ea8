import api from '@/api/service.js'

// 获取课程分页列表
export const getCourseList = (params = {}) => {
  // 默认参数
  const defaultParams = {
    pageNum: 1,
    pageSize: 16,
    //status: 1 //（测试阶段不用取消注释，投入使用的时候需要取消注释）
  };
  
  // 过滤掉空值参数并合并默认参数
  const filteredParams = Object.fromEntries(
    Object.entries({
      ...defaultParams,
      ...params
    }).filter(([_, v]) => v !== '' && v !== undefined && v !== null)
  );
  
  // 特殊处理学期参数
  if (filteredParams.semester === 'all') {
    delete filteredParams.semester;
  }
  
  return api({
    url: '/course/list',
    method: 'get',
    params: filteredParams
  });
}

// 获取课程详情
export const getCourseDetail = (id) => {
  return api({
    url: `/course/detail?id=${id}`,
    method: 'get'
  });
};

// 获取课程中心课程信息详情（课程目标等）
export const getCourseInfoList = (params = {}) => {
  // 默认参数
  const defaultParams = {
    pageNum: 1,
    pageSize: 1
  };
  const filteredParams = Object.fromEntries(
    Object.entries({
      ...defaultParams,
      ...params
    }).filter(([_, v]) => v !== '' && v !== undefined && v !== null)
  );
  return api({
    url: '/course/list',
    method: 'get',
    params: filteredParams
  });
}