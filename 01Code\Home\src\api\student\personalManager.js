//学生端个人管理
import request from '@/api/service'

// 通过学生ID获取学生信息
export const getStudentInfo = (id) => {
  return request({
    url: '/student/get',
    method: 'get',
    params: { id }
  })
}

// 修改学生信息
export const updateStudentInfo = (data) => {
  return request({
    url: '/student/update',
    method: 'post',
    data
  })
}

// 上传学生证件照
export const uploadStudentPhoto = (file) => {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/student/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

