<!--src\views\teacher\CourseDetailManager\CourseManagement\index.vue-->
<template>
  <div class="course-management-container">
    <div class="action-buttons">
      <div class="button-wrapper" @click="handleEndCourse">
        <div class="icon-container">
          <svg class="custom-icon" viewBox="0 0 24 24">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
          </svg>
        </div>
        <span class="button-text">结课归档</span>
      </div>

      <div class="button-wrapper" @click="handleExportCourse">
        <div class="icon-container">
          <svg class="custom-icon" viewBox="0 0 24 24">
            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
          </svg>
        </div>
        <span class="button-text">课程数据导出</span>
      </div>

      <div class="button-wrapper" @click="handleDeleteCourse">
        <div class="icon-container">
          <svg class="custom-icon" viewBox="0 0 24 24">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
        </div>
        <span class="button-text">删除课程</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/api/service'

const route = useRoute()
const router = useRouter()
const courseId = route.params.courseId

const handleDeleteCourse = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除此课程吗？此操作不可撤销！',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await request.post('/course/remove', { id: courseId })
    
    if (response.code === 0) {
      ElMessage.success(response.msg || '课程删除成功')
      router.push('/teacher/teach-class')
    } else {
      ElMessage.error(response.msg || '课程删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除课程失败:', error)
      ElMessage.error(error.response?.data?.msg || error.message || '删除课程失败')
    }
  }
}

// 其他方法
const handleEndCourse = () => {
  // 结课归档逻辑
}

const handleExportCourse = () => {
  // 课程导出逻辑
}
</script>

<style scoped>
.course-management-container {
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中 */
  align-items: center; /* 水平居中 */
  min-height: 80vh; /* 确保容器至少和视口一样高 */
  padding: 20px;
  box-sizing: border-box; /* 防止padding影响高度计算 */
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 54px;
  width: 100%;
  max-width: 1800px; /* 限制最大宽度 */
}

.button-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 230px;
  padding: 100px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.button-wrapper:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 结课归档按钮样式 */
.button-wrapper:nth-child(1) {
  background-color: #f0f7ff;
  border: 1px solid #d0e3ff;
  color: #409eff;
}

/* 课程导出按钮样式 */
.button-wrapper:nth-child(2) {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  color: #606266;
}

/* 删除课程按钮样式 */
.button-wrapper:nth-child(3) {
  background-color: #fff0f0;
  border: 1px solid #ffd0d0;
  color: #f56c6c;
}

.icon-container {
  margin-bottom: 12px;
}

.custom-icon {
  width: 64px;
  height: 64px;
  fill: currentColor;
}

.button-text {
  font-size: 16px;
  font-weight: 500;
}
</style>