<template>
  <div class="statistics-view">
    <el-card class="box-card">
      <div class="stats-container">
        <div class="stat-item">
          <div class="text item">
            <span>历史发起次数</span>
            <div class="percentage-value">{{ historyCount }}%</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="text item">
            <span>平均到课率</span>
            <div class="percentage-value">{{ attendanceRate }}%</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="text item">
            <span>平均旷课率</span>
            <div class="percentage-value">{{ absenceRate }}%</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="text item">
            <span>平均请假率</span>
            <div class="percentage-value">{{ leaveRate }}%</div>
          </div>
        </div>
      </div>
      
      <!-- 图表区域 -->
      <div class="chart-container">
        <div ref="chartRef" style="height: 300px;"></div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';

// 定义统计数据
const historyCount = ref(2);
const attendanceRate = ref(75);
const absenceRate = ref(25);
const leaveRate = ref(0);
const chartRef = ref(null);

// 图表数据
const chartData = {
  labels: ['课程签到第1周', '课程签到第2周', '课程签到第3周', '课程签到第4周', '课程签到第5周'],
  datasets: [
    {
      label: '到课率',
      backgroundColor: 'lightblue',
      data: [100, 80, 60, 40, 50]
    },
    {
      label: '旷课率',
      backgroundColor: 'orange',
      data: [0, 20, 40, 60, 50]
    },
    {
      label: '请假率',
      backgroundColor: 'yellow',
      data: [0, 0, 0, 0, 0]
    }
  ]
};

// 初始化图表
onMounted(() => {
  if (chartRef.value) {
    const chart = echarts.init(chartRef.value);
    chart.setOption({
      title: { text: '签到率趋势图' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: chartData.labels
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100
      },
      series: chartData.datasets.map(dataset => ({
        name: dataset.label,
        type: 'line',
        data: dataset.data,
        itemStyle: { color: dataset.backgroundColor }
      }))
    });
  }
});
</script>

<style scoped lang="scss">
.statistics-view {
  height: 100%;
  box-sizing: border-box;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.box-card {
  width: 100%;
  box-sizing: border-box;
}

// 统计项容器 - 使用flex布局
.stats-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px; // 间距
  margin-bottom: 24px;
}

// 单个统计项样式
.stat-item {
  flex:0 0 24%; // 宽度为25%
  min-width: 200px; // 最小宽度
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background-color: #f9fafb;
  transition: transform 0.3s;
  
  &:hover {
    transform: translateY(-4px);
  }
  
  .text {
    width: 100%;
    text-align: center;
    
    .percentage-value {
      font-size: 24px;
      font-weight: bold;
      color: #409EFF;
    }
  }
}

// 图表容器
.chart-container {
  margin-top: 20px;
  width: 100%;
}
</style>