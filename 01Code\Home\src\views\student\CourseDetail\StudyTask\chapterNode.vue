<!-- src\views\public\course\Course_Center\Chapter\ChapterNode.vue -->
<template>
  <div :id="anchorId" :class="['chapter-node', depth > 0 ? 'nested' : '']">
    <!-- 节点标题行 -->
    <div 
      class="node-header"
      :class="{ 'top-level': depth === 0 }"
      @click="toggleExpanded"
    >
      <!-- 状态图标（仅二级及以下章节显示） -->
       <div>
            <!-- 修改状态图标为圆形进度条 -->
            <span v-if="depth > 0" class="progress-circle" :style="progressStyle">
              <svg viewBox="0 0 36 36" class="circular-chart">
                <path class="circle-bg"
                  d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path class="circle-fill"
                  :stroke-dasharray="`${progress}, 100`"
                  d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                />
              </svg>
            </span>
            <!-- 章节编号 -->
            <span class="chapter-number">
                {{ formattedChapterNumber }}
            </span>
            <!-- 标题文本 -->
            <span 
                class="title-text"
                :class="{
                'highlighted': isHighlighted,
                'large-text': depth === 0
                }"
            >
                {{ node.title }}
            </span>
       </div>
      <div>
        <span v-if="isExpandable" class="expand-icon" :class="{ 'low-opacity': !hasChildren && !hasVideos }">
          <svg v-if="isExpanded" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </span>
        <span v-else class="expand-placeholder"></span>
      </div>
    </div>
    
    <!-- 子节点 -->
    <div v-if="isExpanded && hasChildren" class="children-container">
      <chapterNode
        v-for="(child, index) in node.children"
        :key="index"
        :node="child"
        :depth="depth + 1"
        :search-query="searchQuery"
        @preview-resource="$emit('preview-resource', $event)"
        @video-clicked="$emit('video-clicked', $event)"
      />
    </div>
    
    <!-- 视频资源列表 -->
    <div v-if="isExpanded && videoResources.length > 0" class="resource-list">
      <div 
        v-for="(resource, index) in videoResources" 
        :key="resource.id"
        class="resource-item"
        :class="{ 'watched': isVideoWatched(resource.id) }"
      >
        <div class="resource-content">
          <span class="resource-type-tag" :class="resourceTypeClass(resource)">
            {{ resourceTypeText(resource) }}
          </span>
          <i :class="resourceIcon(resource)" class="resource-icon"></i>
          <span 
            class="resource-title"
            @click="handleVideoClick(resource)"
          >
            {{ resource.videoTitle }}
          </span>
          <span class="video-duration">
            {{ formatDuration(resource.videoDuration) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'
import { getVideoListByChapter,getStudentChapterProgress  } from '@/api/student/course' // 新增导入视频列表接口
import iconProgress1 from '@/assets/img/Student/icon-progress-1.png'
import iconProgress2 from '@/assets/img/Student/icon-progress-2.png'
import iconProgress3 from '@/assets/img/Student/icon-progress-3.png'

const props = defineProps({
  anchorId: String,
  node: Object,
  depth: { type: Number, default: 0 },
  searchQuery: String,
  courseId: String
})

const emit = defineEmits(['preview-resource', 'video-clicked'])

// 状态
const isExpanded = ref(false)
const videoResources = ref([]) // 改为直接存储视频资源
const loading = ref(false)
const chapterProgress = ref(0) // 章节进度百分比

// 计算属性
const hasChildren = computed(() => props.node.children?.length > 0)
const hasVideos = computed(() => videoResources.value.length > 0)
const isExpandable = computed(() => hasChildren.value || props.node.id)
const isHighlighted = computed(() => 
  props.searchQuery && props.node.title.toLowerCase().includes(props.searchQuery.toLowerCase())
)
const formattedChapterNumber = computed(() => 
  props.depth === 0 ? `第${props.node.chapterNumber}章` : `${props.node.chapterNumber}.${props.node.sortOrder}`
)

// 格式化视频时长
const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs < 10 ? '0' : ''}${secs}`
}

// 获取状态图标
const getStatusIcon = () => {
  if (videoResources.value.length === 0) return iconProgress1
  
  const watchedCount = videoResources.value.filter(v => isVideoWatched(v.id)).length
  
  if (watchedCount === 0) {
    return iconProgress1
  } else if (watchedCount < videoResources.value.length) {
    return iconProgress2
  } else {
    return iconProgress3
  }
}

// 计算属性
const progress = computed(() => {
  // 如果没有进度数据，返回0
  return chapterProgress.value || 0
})

const progressStyle = computed(() => {
  // 根据进度返回不同的颜色
  return {
    '--progress-color': progress.value > 0 ? '#4CAF50' : '#999'
  }
})

// 检查视频是否已观看
const isVideoWatched = (videoId) => {
  return localStorage.getItem(`video-watched-${videoId}`) !== null
}

// 资源相关方法
const resourceTypeText = () => '视频'
const resourceTypeClass = (resource) => isVideoWatched(resource.id) ? 'video-watched' : 'video'
const resourceIcon = () => 'el-icon-video-camera'

const toggleExpanded = async () => {
  if (!isExpandable.value) return
  
  isExpanded.value = !isExpanded.value
  if (isExpanded.value && videoResources.value.length === 0) {
    await fetchVideoResources()
  }
}

// 计算学习进度
const calculateProgress = () => {
  if (videoResources.value.length === 0) {
    return 0 // 没有视频时显示0%
  }
  
  const completedCount = videoResources.value.filter(
    video => video.isWatchCompleted === 1
  ).length
  
  return Math.round((completedCount / videoResources.value.length) * 100)
}

// 监听视频资源变化自动更新进度
watch(videoResources, () => {
  chapterProgress.value = calculateProgress()
}, { deep: true })

// 获取视频列表时处理进度
const fetchVideoResources = async () => {
  if (!props.node.id) return
  
  loading.value = true
  try {
    const res = await getVideoListByChapter({ chapterId: props.node.id })
    if (res.code === 200) {
      videoResources.value = res.result || []
      chapterProgress.value = calculateProgress() // 初始化计算进度
    }
  } finally {
    loading.value = false
  }
}

// 点击视频时更新状态
const handleVideoClick = (resource) => {
  console.log('Video resource:', resource) // 调试用
  emit('video-clicked', {
    id: resource.id,
    videoTitle: resource.videoTitle,
    videoUrl: resource.videoUrl,
    duration: resource.duration // 使用正确的字段名
  })
  emit('preview-resource', {
    id: resource.id,
    name: resource.videoTitle,
    type: 1,
    url: resource.videoUrl,
    duration: resource.duration // 使用正确的字段名
  })
}


onMounted(async () => {
  // 组件挂载时获取章节进度
  await fetchChapterProgress()
})

// 获取章节进度
const fetchChapterProgress = async () => {
  if (!props.courseId || !props.node.id) return
  
  try {
    const res = await getStudentChapterProgress(props.courseId)
    if (res.code === 200) {
      const chapter = res.result.find(c => c.id === props.node.id)
      if (chapter) {
        // 使用watchProgress字段作为进度百分比
        chapterProgress.value = chapter.watchProgress || 0
      }
    }
  } catch (error) {
    console.error('获取章节进度失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center; // 确保所有子元素垂直居中
  padding: 10px 0;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 200ms;
  margin-top: 10px;
  
  > div {
    display: flex;
    align-items: center; // 确保内部元素垂直居中
  }
  
  &:hover {
    background-color: #f9fafb;
  }
  
  &.top-level {
    background-color: #f5f5ff;
    padding: 15px 0 15px 15px;
    border-radius: 0.5rem;
    
    &:hover {
      background-color: #f1f1fd;
    }
  }
}

.status-icon {
  margin-right: 0.5rem;
  width: 16px;
  height: 16px;
  display: inline-flex; 
  align-items: center;
  
  img {
    margin-top: 0;
    width: 100%;
    height: 100%;
  }
}

.expand-icon {
  margin-right: 20px;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  
  svg {
    transition: transform 0.2s;
  }
  
  &.low-opacity {
    opacity: 0.3;
  }
}

.expand-placeholder {
  margin-right: 0.5rem;
  width: 1rem;
}

.title-text {
  font-weight: 500;
  color: #1f2937;
  
  &.highlighted {
    color: #2563eb;
  }
  
  &.large-text {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

.resource-stats {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.resource-count {
  padding: 0.125rem 0.375rem;
  background-color: #d1d1ff;
  color: $primary-color;
  border-radius: 9999px;
  font-size: 0.75rem;
}

.children-container {
  margin-left: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.resource-list {
  margin-top: 5px;
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.resource-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  border-radius: 0.375rem;
  transition: all 200ms;
    .resource-content {
    display: flex;
    align-items: center;
        .resource-type-tag {
        margin-right: 0.75rem;
        padding: 0.125rem 0.375rem;
        border-radius: 0.25rem;
        font-size: 0.85rem;
        font-weight: normal;
        color: #9e9e9e;
        background-color: #f5f5f5;
        
        &.video-watched {
            color: #4CAF50;
            //background-color: #e8f5e9;
            border: #4CAF50 1px solid;
        }
        }
        .resource-title {
            cursor: pointer;
            &:hover {
            color: $primary-color;
            }
        }
        .video-duration {
          margin-left: auto;
          color: #6b7280;
          font-size: 0.85rem;
        }

    }

    &:hover {
        background-color: #f9fafb;
    }
    
    &.watched {
        .resource-type-tag {
        color: #4CAF50;
        //background-color: #e8f5e9;
        }
        
        .resource-icon {
        color: #4CAF50;
        }
    }
}
.progress-circle {
  margin-right: 0.5rem;
  width: 16px;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  .circular-chart {
    display: block;
    width: 100%;
    height: 100%;
    
    .circle-bg {
      fill: none;
      stroke: #eee;
      stroke-width: 2;
    }
    
    .circle-fill {
      fill: none;
      stroke: var(--progress-color);
      stroke-width: 2;
      stroke-linecap: round;
      animation: progress 1s ease-out forwards;
    }
  }
}

@keyframes progress {
  0% {
    stroke-dasharray: 0, 100;
  }
}
</style>