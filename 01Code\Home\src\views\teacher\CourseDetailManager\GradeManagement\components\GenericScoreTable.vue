<template>
  <div class="score-container">
    <!-- 顶部控制栏 -->
    <div class="control-bar">
      <div class="class-selector">
        <label for="class-select">班级：</label>
        <select id="class-select" v-model="selectedClass">
          <option v-for="classItem in classes" :key="classItem.id" :value="classItem.id">
            {{ classItem.name }}
          </option>
        </select>
      </div>
      
      <div class="right-controls">
        <button class="download-btn" @click="exportData">
          <span class="icon">↓</span> 下载{{ title }}
        </button>
        
        <div class="search-box">
          <input
            type="text"
            placeholder="请输入学生姓名或学号"
            v-model="searchQuery"
            @input="filterStudents"
          />
          <span class="search-icon">🔍</span>
        </div>
      </div>
    </div>

    <!-- 数据截止时间（可选） -->
    <div class="data-cutoff" v-if="cutoffDate">
      数据截止到 {{ cutoffDate }}
    </div>

    <!-- 成绩表格 -->
    <div class="table-container">
      <table class="score-table">
        <thead>
          <tr>
            <th 
              v-for="column in columns" 
              :key="column.key"
              @click="sortBy(column.key)"
              :class="{ sortable: column.sortable }"
            >
              {{ column.title }}
              <span 
                class="sort-icon" 
                v-if="column.sortable"
                :class="{ active: sortField === column.key }"
              >
                {{ sortOrder === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in filteredData" :key="item.id">
            <td 
              v-for="column in columns" 
              :key="column.key"
              :class="column.class ? column.class(item[column.key]) : ''"
            >
              {{ formatCell(item[column.key], column) }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  title: String,
  columns: Array,
  data: Array,
  classes: Array,
  cutoffDate: String
})

// 状态管理
const selectedClass = ref(props.classes[0]?.id || '')
const searchQuery = ref('')
const sortField = ref('id')
const sortOrder = ref('asc')

// 过滤和排序数据
const filteredData = computed(() => {
  let result = [...props.data]
  
  // 班级筛选
  if (selectedClass.value) {
    result = result.filter(item => item.classId === selectedClass.value)
  }
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(item => 
      item.name.toLowerCase().includes(query) || 
      item.id.toLowerCase().includes(query)
    )
  }
  
  // 排序
  if (sortField.value) {
    result.sort((a, b) => {
      let comparison = 0
      if (a[sortField.value] > b[sortField.value]) {
        comparison = 1
      } else if (a[sortField.value] < b[sortField.value]) {
        comparison = -1
      }
      return sortOrder.value === 'asc' ? comparison : -comparison
    })
  }
  
  return result
})

// 方法
const sortBy = (field) => {
  if (sortField.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortField.value = field
    sortOrder.value = 'asc'
  }
}

const exportData = () => {
  console.log(`导出${props.title}数据`)
  // 实际导出逻辑
}

const formatCell = (value, column) => {
  if (column.formatter) {
    return column.formatter(value)
  }
  return value
}
</script>

<style lang="scss" scoped>
.score-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Microsoft YaHei', sans-serif;
  padding: 20px;
}

.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
  
  .class-selector {
    select {
      padding: 8px 12px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-size: 14px;
      min-width: 180px;
    }
  }
  
  .right-controls {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .download-btn {
    padding: 8px 15px;
    background-color: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #66b1ff;
    }
    
    .icon {
      font-weight: bold;
    }
  }
  
  .search-box {
    position: relative;
    
    input {
      padding: 8px 12px 8px 32px;
      border-radius: 4px;
      border: 1px solid #ddd;
      width: 250px;
      font-size: 14px;
    }
    
    .search-icon {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

.data-cutoff {
  color: #888;
  font-size: 14px;
  margin-bottom: 10px;
  padding-left: 5px;
}

.table-container {
  width: 100%;
  overflow-x: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.score-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
  
  th, td {
    padding: 12px 15px;
    text-align: center;
    border-bottom: 1px solid #eee;
  }
  
  th {
    background-color: #f8f9fa;
    font-weight: 600;
    white-space: nowrap;
    cursor: pointer;
    
    &:first-child {
      text-align: left;
    }
    
    .sort-icon {
      opacity: 0;
      margin-left: 5px;
      transition: opacity 0.2s;
      
      &.active {
        opacity: 1;
      }
    }
    
    &:hover .sort-icon {
      opacity: 0.5;
    }
  }
  
  td {
    &:first-child {
      text-align: left;
    }
    
    &.total-score {
      font-weight: bold;
      color: #409eff;
    }
  }
  
  tr:hover {
    background-color: #f8f9fa;
  }
}
</style>