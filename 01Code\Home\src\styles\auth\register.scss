/*@/styles/auth/register.scss*/
@use '@/styles/variables.scss' as *;

.register-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: $bg-color;
  padding: 20px;
  
  h2 {
    color: $text-color;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 10px;
    text-align: center;
  }
  
  form {
    width: 100%;
    max-width: 400px;
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.07);
    padding: 40px 30px;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1), 0 8px 10px rgba(0, 0, 0, 0.08);
    }
    
    div {
      margin-bottom: 20px;
      
      label {
        display: block;
        color: $text-color;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
      }
      
      input, select {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid $border-color;
        border-radius: 8px;
        font-size: 14px;
        color: $text-color;
        transition: all 0.2s ease;
        
        &:focus {
          outline: none;
          border-color: $primary-color;
          box-shadow: 0 0 0 3px rgba($primary-color, 0.2);
        }
        
        &::placeholder {
          color: rgba($text-color, 0.4);
        }
      }
      
      select {
        appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%239CA3AF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 15px center;
        background-size: 16px;
      }
    }
    
    button {
      width: 100%;
      background: $primary-color;
      color: white;
      font-weight: 500;
      padding: 12px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 10px;
      
      &:hover {
        background: $primary-color;
        opacity: 0.9;
        transform: scale(1.02);
      }
      
      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none !important;
      }
    }
    
    .error-message {
      color: $error-color;
      font-size: 14px;
      margin-top: 10px;
      text-align: center;
    }
    .success-message {
      color: green;
      text-align: center;
      margin-top: 10px;
    }
    .login-link {
      text-align: center;
      margin-top: 20px;
      color: rgba($text-color, 0.7);
      font-size: 14px;
      
      a {
        color: $primary-color;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
        
        &:hover {
          text-decoration: underline;
          opacity: 0.8;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .register-container {
    form {
      padding: 30px 20px;
    }
  }
}