<!-- src\views\public\course\Course_Center\Chapter\index.vue -->
<template>
  <div class="chapter-container">
    <!-- 章节列表模式 -->
    <div v-if="!currentVideo" class="chapter-content">
      <div v-if="loading" class="loading">
        <i class="el-icon-loading"></i> 加载中...
      </div>
      
      <div v-else class="chapter-list">
        <chapterNode
          v-for="(chapter, index) in filteredChapters"
          :key="index"
          :node="chapter"
          :depth="0"
          :search-query="searchQuery"
          :course-id="courseId"
          :anchor-id="`chapter-${chapter.id}`"
          @preview-resource="handlePreview"
          @video-clicked="handleVideoClicked"
        />
      </div>
      
      <div v-if="!loading && filteredChapters.length === 0" class="no-results">
        <i class="fa fa-search-minus"></i> 没有找到匹配的内容
      </div>
    </div>

    <!-- 视频播放模式 -->
    <VideoPlayer 
      v-else 
      :video-data="currentVideo" 
      @close="closeVideoPlayer"
    />

    <!-- 下载对话框 -->
    <el-dialog
      title="下载视频"
      :visible.sync="downloadDialogVisible"
      width="30%"
    >
      <div class="download-dialog">
        <el-form>
          <el-form-item label="文件名">
            <el-input v-model="downloadFileName" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="保存位置">
            <el-button @click="selectDownloadFolder">选择文件夹</el-button>
            <span class="folder-path">{{ downloadFolderPath || '未选择' }}</span>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="downloadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmDownload">下载</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getCourseChapters } from '@/api/student/course'
import chapterNode from './chapterNode.vue'
import VideoPlayer from './VideoPlayer.vue'
import { ElMessage } from 'element-plus'

const route = useRoute()
const courseId = route.params.courseId

// 数据状态
const loading = ref(true)
const chapters = ref([])
const searchQuery = ref('')
const currentVideo = ref(null)
const watchedVideos = ref(new Set())

// 下载相关状态
const downloadDialogVisible = ref(false)
const downloadFileName = ref('')
const downloadFolderPath = ref('')

// 格式化视频时长
const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs < 10 ? '0' : ''}${secs}`
}

// 获取课程章节数据
const fetchChapters = async () => {
  try {
    loading.value = true
    const res = await getCourseChapters(courseId)
    
    if (res.code === 200) {
      chapters.value = formatChapters(res.result || [])
    } else {
      console.error('获取章节失败:', res.msg)
    }
  } catch (error) {
    console.error('请求章节出错:', error)
  } finally {
    loading.value = false
  }
}

// 格式化章节数据
const formatChapters = (nodes) => {
  return nodes.map(node => ({
    ...node,
    children: node.children ? formatChapters(node.children) : []
  }))
}

// 过滤章节列表
const filteredChapters = computed(() => {
  if (!searchQuery.value.trim()) return chapters.value
  
  function searchInNode(node) {
    const isNodeMatch = node.title.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    let childrenMatch = []
    if (node.children && node.children.length > 0) {
      childrenMatch = node.children
        .map(child => searchInNode(child))
        .filter(matched => matched !== null)
    }
    
    if (isNodeMatch || childrenMatch.length > 0) {
      return {
        ...node,
        children: childrenMatch.length > 0 ? childrenMatch : node.children
      }
    }
    
    return null
  }
  
  return chapters.value
    .map(chapter => searchInNode(chapter))
    .filter(chapter => chapter !== null)
})

const handlePreview = (resource) => {
  currentVideo.value = {
    id: resource.id,
    videoTitle: resource.name,
    videoUrl: resource.url
  }
}

// 处理视频点击事件
const handleVideoClicked = (videoData) => {
  currentVideo.value = {
    id: videoData.id,
    videoTitle: videoData.videoTitle,
    videoUrl: videoData.videoUrl
  }
}

// 关闭视频播放器
const closeVideoPlayer = () => {
  currentVideo.value = null
}

// 处理下载按钮点击
const handleDownload = () => {
  downloadFileName.value = currentVideo.value.videoTitle
  downloadDialogVisible.value = true
}

// 选择下载文件夹
const selectDownloadFolder = async () => {
  try {
    // 这里使用 Electron 的 dialog 或者浏览器 API
    // 实际实现可能需要根据你的环境调整
    if (window.electron) {
      const result = await window.electron.showOpenDialog({
        properties: ['openDirectory']
      })
      if (!result.canceled && result.filePaths.length > 0) {
        downloadFolderPath.value = result.filePaths[0]
      }
    } else {
      // 浏览器环境下的替代方案
      const dirHandle = await window.showDirectoryPicker()
      downloadFolderPath.value = dirHandle.name
    }
  } catch (error) {
    console.error('选择文件夹出错:', error)
    ElMessage.error('选择文件夹失败')
  }
}

// 确认下载
const confirmDownload = () => {
  if (!downloadFolderPath.value) {
    ElMessage.warning('请选择保存位置')
    return
  }
  
  // 这里实现实际的下载逻辑
  console.log('开始下载:', {
    fileName: downloadFileName.value,
    folderPath: downloadFolderPath.value,
    videoUrl: currentVideo.value.videoUrl
  })
  
  ElMessage.success('下载任务已开始')
  downloadDialogVisible.value = false
}

onMounted(() => {
  fetchChapters()
})
</script>

<style lang="scss" scoped>
.chapter-container {
  background: white;
  border-radius: 8px;
  height: 100vh;
  overflow: hidden;
}

.chapter-content {
  padding: 30px;
  height: 100%;
  overflow-y: auto;
}

.video-player-mode {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-player-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0; /* 移除内边距 */
}

.video-player {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  width: 100%; /* 确保宽度填满 */
  height: calc(100% - 120px); /* 减去信息栏和按钮的高度 */
  
  video {
    width: 100%; /* 视频宽度填满容器 */
    height: 100%; /* 视频高度填满容器 */
    object-fit: contain; /* 保持原始比例，完整显示视频 */
  }
}

.video-info {
  padding: 10px 20px;
  color: $text-color;
  
  h3 {
    margin: 0 0 5px 0;
    font-size: 18px;
  }
  
  .video-duration {
    color: $text-color;
    font-size: 14px;
  }
}

.video-actions {
  display: flex;
  justify-content: space-between;
  .back-button {
      margin-left: 20px;
      margin-bottom: 20px;
  }
  .download-button{
      margin-right: 20px;
      margin-bottom: 20px;
  
  }
}

.download-dialog {
  .folder-path {
    margin-left: 10px;
    color: #666;
    font-size: 14px;
  }
}

.loading {
  padding: 20px;
  text-align: center;
  color: #666;
}

.el-icon-loading {
  margin-right: 8px;
  animation: rotating 2s linear infinite;
}

.no-results {
  text-align: center;
  padding: 40px 0;
  color: #6c757d;
  font-size: 14px;
  
  i {
    margin-right: 8px;
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>