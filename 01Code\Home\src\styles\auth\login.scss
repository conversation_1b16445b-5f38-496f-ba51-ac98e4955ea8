/*@/styles/auth/login.scss;*/
@use 'sass:math';
@use '@/styles/variables.scss' as *;

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: $bg-color;
  padding: 20px;
  
  .login-card {
    width: 100%;
    max-width: 400px;
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.07);
    padding: 40px 30px;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1), 0 8px 10px rgba(0, 0, 0, 0.08);
    }
    
    .login-header {
      text-align: center;
      margin-bottom: 30px;
      
      .login-title {
        color: $text-color;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 10px;
      }
      
      .login-subtitle {
        color: rgba($text-color, 0.7);
        font-size: 16px;
      }
    }
    
    .login-form {
      .form-group {
        margin-bottom: 20px;
        
        .form-label {
          display: block;
          color: $text-color;
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 8px;
        }
        
        .form-input-container {
          position: relative;
          
          .form-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba($text-color, 0.5);
            font-size: 16px;
          }
          
          .form-input {
            width: 100%;
            padding: 12px 15px 12px 40px;
            border: 1px solid $border-color;
            border-radius: 8px;
            font-size: 14px;
            color: $text-color;
            transition: all 0.2s ease;
            
            &:focus {
              outline: none;
              border-color: $primary-color;
              box-shadow: 0 0 0 3px rgba($primary-color, 0.2);
            }
            
            &::placeholder {
              color: rgba($text-color, 0.4);
            }
          }
          
          select.form-input {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%239CA3AF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 15px center;
            background-size: 16px;
          }
        }
      }
      
      .captcha-container {
        display: flex;
        gap: 15px;
        
        .captcha-input {
          flex: 1;
          padding: 12px 15px;
        }
        
        .captcha-image {
          height: 48px;
          min-width: 120px;
          border-radius: 8px;
          border: 1px solid $border-color;
          cursor: pointer;
          transition: all 0.2s ease;
          
          &:hover {
            opacity: 0.9;
            transform: scale(1.02);
          }
        }
      }
      
      .login-button {
        width: 100%;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:disabled {
          opacity: 0.7;
          cursor: not-allowed;
          transform: none !important;
        }
      }
    }
    
    .login-footer {
      margin-top: 25px;
      text-align: center;
      
      .register-link {
        color: rgba($text-color, 0.7);
        font-size: 14px;
        
        a {
          text-decoration: none;
          font-weight: 500;
          transition: all 0.2s ease;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.4s ease forwards;
}

// 响应式设计
@media (max-width: 480px) {
  .login-container {
    .login-card {
      padding: 30px 20px;
    }
  }
}