<template>
  <div class="scrollable-table-container">
    <!-- 顶部控制栏 -->
    <div class="control-bar">
      <div class="class-selector">
        <label for="class-select">班级：</label>
        <select id="class-select" v-model="selectedClass">
          <option v-for="classItem in classes" :key="classItem.id" :value="classItem.id">
            {{ classItem.name }}
          </option>
        </select>
      </div>

      <div class="right-controls">
        <button class="download-btn" @click="exportScores">
          <span class="icon">↓</span> 下载{{ title }}
        </button>

        <div class="search-box">
          <input
            type="text"
            :placeholder="`请输入学生姓名或学号`"
            v-model="searchQuery"
            @input="filterStudents"
          />
          <span class="search-icon">🔍</span>
        </div>
      </div>
    </div>

    <!-- 数据截止时间 -->
    <div v-if="cutoffDate" class="data-cutoff">
      数据截止到 {{ cutoffDate }}
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
      <!-- 表格导航控制 -->
      <div class="table-navigation">
        <button 
          class="nav-button left" 
          @click="scrollTable(-1)"
          :disabled="scrollPosition === 0"
        >
          &lt;
        </button>
        <button 
          class="nav-button right" 
          @click="scrollTable(1)"
          :disabled="isScrollEnd"
        >
          &gt;
        </button>
      </div>

      <!-- 成绩表格容器 -->
      <div class="table-wrapper" ref="tableWrapper" @scroll="handleScroll">
        <table class="score-table" ref="table">
          <thead>
            <tr>
              <th 
                v-for="column in columns" 
                :key="column.key"
                :style="{ minWidth: column.minWidth || '300px' }"
                @click="sortBy(column.key)"
                :class="{ sortable: column.sortable }"
              >
                {{ column.title }}
                <span 
                  v-if="column.sortable" 
                  class="sort-icon" 
                  :class="{ 'active': sortField === column.key }"
                >
                  {{ sortOrder === 'asc' ? '↑' : '↓' }}
                </span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(student, index) in filteredData" :key="index">
              <td 
                v-for="column in columns" 
                :key="column.key"
                :class="getCellClass(student[column.key], column)"
              >
                {{ formatCellValue(student[column.key], column) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'

const props = defineProps({
  title: String,
  columns: Array,
  data: Array,
  classes: Array,
  cutoffDate: String
})

const selectedClass = ref(1)
const searchQuery = ref('')
const sortField = ref('id')
const sortOrder = ref('asc')
const scrollPosition = ref(0)
const isScrollEnd = ref(false)
const tableWrapper = ref(null)
const table = ref(null)

// 计算属性
const filteredData = computed(() => {
  let result = [...props.data]
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(s => 
      (s.name && s.name.toLowerCase().includes(query)) || 
      (s.id && s.id.toLowerCase().includes(query))
    )
  }
  
  // 排序
  if (sortField.value) {
    result.sort((a, b) => {
      let comparison = 0
      if (a[sortField.value] > b[sortField.value]) {
        comparison = 1
      } else if (a[sortField.value] < b[sortField.value]) {
        comparison = -1
      }
      return sortOrder.value === 'asc' ? comparison : -comparison
    })
  }
  
  return result
})

// 方法
const sortBy = (field) => {
  if (sortField.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortField.value = field
    sortOrder.value = 'asc'
  }
}

const filterStudents = () => {
  // 搜索功能已经在计算属性中实现
}

const exportScores = () => {
  console.log(`导出${props.title}`)
}

const scrollTable = (direction) => {
  if (!tableWrapper.value) return
  
  const containerWidth = tableWrapper.value.clientWidth
  const scrollAmount = containerWidth * 0.8
  
  const newPosition = scrollPosition.value + (scrollAmount * direction)
  scrollPosition.value = Math.max(0, Math.min(newPosition, table.value.scrollWidth - containerWidth))
  
  tableWrapper.value.scrollTo({
    left: scrollPosition.value,
    behavior: 'smooth'
  })
}

const handleScroll = () => {
  if (!tableWrapper.value) return
  
  scrollPosition.value = tableWrapper.value.scrollLeft
  checkScrollEnd()
}

const checkScrollEnd = () => {
  if (!tableWrapper.value || !table.value) return
  
  const containerWidth = tableWrapper.value.clientWidth
  const scrollWidth = table.value.scrollWidth
  const currentScroll = tableWrapper.value.scrollLeft
  
  // 更新滚动状态
  isScrollEnd.value = currentScroll + containerWidth >= scrollWidth - 1
}

const formatCellValue = (value, column) => {
  if (column.formatter) {
    return column.formatter(value)
  }
  return value
}

const getCellClass = (value, column) => {
  if (column.class) {
    if (typeof column.class === 'function') {
      return column.class(value)
    }
    return column.class
  }
  return ''
}

// 生命周期钩子
onMounted(() => {
  checkScrollEnd()
  window.addEventListener('resize', checkScrollEnd)
})

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    checkScrollEnd()
    // 重置滚动位置
    if (tableWrapper.value) {
      tableWrapper.value.scrollLeft = 0
      scrollPosition.value = 0
    }
  })
}, { deep: true })
</script>

<style lang="scss" scoped>
.scrollable-table-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
  
  .class-selector {
    select {
      padding: 8px 12px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-size: 14px;
      min-width: 180px;
    }
  }
  
  .right-controls {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .download-btn {
    padding: 8px 15px;
    background-color: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #66b1ff;
    }
    
    .icon {
      font-weight: bold;
    }
  }
  
  .search-box {
    position: relative;
    
    input {
      padding: 8px 12px 8px 32px;
      border-radius: 4px;
      border: 1px solid #ddd;
      width: 250px;
      font-size: 14px;
    }
    
    .search-icon {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

.data-cutoff {
  color: #888;
  font-size: 14px;
  margin-bottom: 10px;
  padding-left: 5px;
}

.table-container {
  position: relative;
  flex-grow: 1;
}

.table-navigation {
  position: absolute;
  right: 10px;
  top: 10px;
  display: flex;
  gap: 5px;
  z-index: 20;
  background: rgba(255, 255, 255, 0.9);
  padding: 5px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  
  .nav-button {
    padding: 5px 10px;
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: rgba(255, 255, 255, 1);
    }
    
    &:disabled {
      cursor: not-allowed;
      background-color: rgba(255, 255, 255, 0.3);
    }
  }
}

.table-wrapper {
  max-height: 500px;
  overflow-x: auto;
  overflow-y: auto;
  padding-bottom: 10px;
}

.score-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  
  th {
    background-color: #f5f5f5;
    padding: 12px 15px;
    text-align: left;
    font-weight: bold;
    cursor: pointer;
    
    .sort-icon {
      margin-left: 5px;
      font-size: 12px;
    }
  }

  td {
    padding: 10px 15px;
    text-align: left;
  }

  tr:nth-child(odd) {
    background-color: #f9f9f9;
  }

  tr:hover {
    background-color: #f1f1f1;
  }
}
</style>
