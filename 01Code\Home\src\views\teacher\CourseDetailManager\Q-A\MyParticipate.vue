<!--src\views\teacher\CourseDetailManager\Q-A\MyParticipate.vue-->
<template>
  <div class="my-participate">
    <div class="tab-header">
      <button 
        v-for="(tab, index) in tabs" 
        :key="index" 
        @click="activeTab = tab.value"
        :class="{ active: activeTab === tab.value }"
      >
        {{ tab.label }}
      </button>
    </div>
    <div class="topic-list">
      <template v-if="activeTab === 'my-answers'">
        <div class="empty-tip" v-if="myAnswers.length === 0">暂无我的回答</div>
        <div 
          v-for="(item, index) in myAnswers" 
          :key="index" 
          class="topic-item"
        >
          <div class="topic-title">{{ item.title }}</div>
          <div class="topic-meta">
            <span>0 人浏览</span>
            <span>0 条回答</span>
            <span>{{ item.updateTime }}</span>
            <button class="set-essence" @click="handleSetEssence(item)">设为精华</button>
          </div>
        </div>
      </template>
      <template v-else>
        <div 
          v-for="(item, index) in myTopics" 
          :key="index" 
          class="topic-item"
        >
          <div class="topic-title">{{ item.title }}</div>
          <div class="topic-meta">
            <span>0 人浏览</span>
            <span>0 条回答</span>
            <span>{{ item.author }}</span>
            <span>{{ item.updateTime }}</span>
            <button class="set-essence" @click="handleSetEssence(item)">设为精华</button>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 标签数据
const tabs = ref([
  { label: '我的回答', value: 'my-answers' },
  { label: '我的话题', value: 'my-topics' }
])
// 激活的标签
const activeTab = ref('my-answers')

// 模拟“我的回答”数据
const myAnswers = ref([]) 

// 模拟“我的话题”数据
const myTopics = ref([
  { title: '1+1=?', author: '杨兵 · 桂林电子科技大学', updateTime: '3 分钟前' },
  { title: '12345', author: '杨兵 · 桂林电子科技大学', updateTime: '15 分钟前' },
  { title: '1111111111', author: '杨兵 · 桂林电子科技大学', updateTime: '54 分钟前' }
])

// 设为精华的逻辑（可扩展，比如调用接口）
const handleSetEssence = (item) => {
  console.log('设为精华：', item)
  // 若要真正标记为精华，可调整数据结构，比如给 item 加标识等
}
</script>

<style lang="scss" scoped>
.my-participate {
  margin-bottom: 20px;
  padding: 30px;
  background-color: white;
  height: 80vh;
  position: relative;
  .tab-header {
    display: flex;
    gap: 10px;

    button {
      padding: 8px 16px;
      border: none; /* 移除边框 */
      background: transparent; /* 透明背景 */
      cursor: pointer;
      color: #666;
      transition: all 0.3s ease;
      position: relative; /* 为了给下划线定位 */

      &:hover {
        color: #333; /* hover 时改变文字颜色，可根据需求调整 */
      }

      &.active {
        color: #333;
      }

      &.active::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 3px;
        background-color: $primary-color; /* 下划线颜色，可根据需求调整 */
      }
    }
  }

  .topic-list {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .topic-item {
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 12px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.03);
      }

      .topic-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #333;
      }

      .topic-meta {
        font-size: 12px;
        color: #909399;
        display: flex;
        align-items: center;
        gap: 10px;

        .set-essence {
          background: #fff;
          border: 1px solid #e5e7eb;
          padding: 4px 8px;
          cursor: pointer;
          border-radius: 4px;
          color: #666;
          transition: all 0.3s ease;

          &:hover {
            background-color: #f9fafb;
            border-color: #c0c4cc;
          }
        }
      }
    }

    .empty-tip {
      color: #999;
      padding: 10px;
    }
  }
}
</style>