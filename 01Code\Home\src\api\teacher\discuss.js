//src\api\teacher\discuss.js
import request from '@/api/service'

// 获取教师发布的话题列表
export const getTeacherPosts = (params) => {
  return request({
    url: '/post/user/posts',
    method: 'get',
    params
  })
}

// 获取用户详情
export const getUserDetail = (userId) => {
  if (!userId) {
    return Promise.reject(new Error('缺少用户ID'))
  }
  
  return request({
    url: '/user/get',
    method: 'get',
    params: { 
      id: String(userId) // 确保转换为字符串
    }
  })
}

// 发布话题
export const createPost = (data) => {
  return request({
    url: '/post/save',
    method: 'post',
    data
  })
}

// 获取话题详情及评论
export const getPostDetailWithComments = (postId, commentPage = 1, commentSize = 10) => {
  return request({
    url: '/post/detail-with-comments',
    method: 'get',
    params: {
      postId,
      commentPage,
      commentSize
    }
  })
}

// 删除话题
export const deletePost = (postId) => {
  return request({
    url: '/post/user/remove',
    method: 'post',
    data: {
      id: postId
    }
  })
}

// 批量删除话题
export const batchDeletePosts = (postIds) => {
  if (!Array.isArray(postIds) || postIds.length === 0) {
    return Promise.reject(new Error('请选择要删除的话题'))
  }
  
  return request({
    url: '/post/user/removeBatch',
    method: 'post',
    data: postIds.map(id => ({ id })), // 转换为包含id字段的对象数组
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 收藏话题
export const starPost = (data) => {
  return request({
    url: '/post/star/save',
    method: 'post',
    data: {
      postId: data.postId,
      userId: data.userId
    },
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 取消收藏 (只需要postId)
export const unstarPost = (starId) => {
  console.log('取消收藏请求参数:', { starId }) // 调试日志
  return request({
    url: '/post/star/remove',
    method: 'post',
    data: {
      id: starId
    },
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 获取收藏的话题列表
export const getStarredPosts = (params) => {
  return request({
    url: '/post/star/list',
    method: 'get',
    params: {
      userId: params.userId, // 确保使用正确的参数名
      pageNum: params.pageNum,
      pageSize: params.pageSize
    }
  })
}

// 获取收藏话题详情
export function getStarDetail(starId) {
  return request({
    url: '/post/star/get',
    method: 'get',
    params: { id: starId }
  })
}

// 检查是否收藏并获取收藏ID
export const checkStarStatus = async (userId, postId) => {
  try {
    const res = await getStarredPosts({
      userId: userId,
      pageNum: 1,
      pageSize: 1000 // 获取足够多的数据确保能找到
    })
    
    if (res.code === 200) {
      const starredPost = res.result.records.find(item => item.postId === postId)
      return {
        isStarred: !!starredPost,
        starId: starredPost ? starredPost.id : null
      }
    }
    return { isStarred: false, starId: null }
  } catch (error) {
    console.error('检查收藏状态失败:', error)
    return { isStarred: false, starId: null }
  }
}

// 回复话题接口
export function saveComment(commentData) {
  return request({
    url: '/comment/save',
    method: 'post',
    data: {
      postId: commentData.postId,
      commenterId: commentData.commenterId,
      content: commentData.content,
      publishTime: commentData.publishTime,
      parentId: commentData.parentId || null // 二级评论有parentId，一级评论为null
    }
  })
}

// 点赞回复
export const likeComment = (commentId) => {
  return request({
    url: '/comment/like/save',
    method: 'post',
    data: {
      commentId: commentId
    }
  })
}

// 取消点赞回复
export const removeLike = (likeId) => {
  return request({
    url: '/comment/like/remove',
    method: 'post',
    data: {
      id: likeId
    }
  })
}

// 获取评论点赞列表
export const getCommentLikes = (params) => {
  return request({
    url: '/comment/like/list',
    method: 'get',
    params: {
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10,
      userId: params.userId, // 当前登录用户ID，用于筛选该用户的点赞
      commentId: params.commentId // 特定评论ID，用于获取该评论的点赞情况
    }
  })
}

// 添加更新话题接口
export const updatePost = (data) => {
  return request({
    url: '/post/user/update',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 更新评论
export const updateComment = (commentData) => {
  return request({
    url: '/comment/user/update',
    method: 'put',
    data: {
      id: commentData.id,
      postId: commentData.postId,
      commenterId: commentData.commenterId,
      content: commentData.content,
      publishTime: commentData.publishTime,
      parentId: commentData.parentId || null
    },
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
